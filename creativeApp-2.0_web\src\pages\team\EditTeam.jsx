import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { alertMessage } from "../../common/coreui";
import Select from "react-select";
import { useDispatch } from 'react-redux';
import { teamApi } from './../../features/api';
import {
  X,
  Users,
  Building,
  User,
  Calendar,
  Clock,
  Upload,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
  const token = localStorage.getItem("token");
  return token !== null;
};

const EditTeam = ({ dataItemsId, isVisible, setVisible }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [team, setTeam] = useState(null);
  const [teamName, setTeamName] = useState("");
  const [icon, setIcon] = useState(null);
  const [existingIcon, setExistingIcon] = useState("");
  const [logo, setLogo] = useState(null);
  const [existingLogo, setExistingLogo] = useState("");
  const [poc, setPoc] = useState("");
  const [manager, setManager] = useState("");
  const [teamLead, setTeamLead] = useState("");
  const [workday, setWorkday] = useState([]);
  const [billableHours, setBillableHours] = useState("");
  const [dailyBillableHours, setDailyBillableHours] = useState("");
  const [weeklyBillableHours, setWeeklyBillableHours] = useState("");
  const [launch, setLaunch] = useState("");
  const [departmentId, setDepartmentId] = useState("");
  const [error, setError] = useState("");
  const [errors, setErrors] = useState({});
  const [formError, setFormError] = useState("");
  const [focusedField, setFocusedField] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [loggedInUser, setLoggedInUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // React Select states
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedPoc, setSelectedPoc] = useState(null);
  const [selectedManager, setSelectedManager] = useState(null);
  const [selectedTeamLead, setSelectedTeamLead] = useState(null);

  // Create options for React Select dropdowns
  const departmentOptions = departments.map(dept => ({
    value: dept.id,
    label: dept.name
  }));

  // Filter based on Responsibility Level (resource_types) instead of roles
  const pocOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  const managerOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Manager', 'HOD'].includes(rt.name || rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  const teamLeadOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  // Debug logs for all options
  console.log('EditTeam - Total users:', users.length);
  console.log('EditTeam - Manager options:', managerOptions.length);
  console.log('EditTeam - POC options:', pocOptions.length);
  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);


  // Days of the week for multi-select
  const daysOfWeek = [
    { value: 'Monday', label: 'Monday' },
    { value: 'Tuesday', label: 'Tuesday' },
    { value: 'Wednesday', label: 'Wednesday' },
    { value: 'Thursday', label: 'Thursday' },
    { value: 'Friday', label: 'Friday' },
    { value: 'Saturday', label: 'Saturday' },
    { value: 'Sunday', label: 'Sunday' }
  ];

  // Handle workday selection
  const handleWorkdayChange = (selectedOptions) => {
    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];
    setWorkday(selectedValues);
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isTokenValid()) {
        setError("No authentication token found.");
        setLoading(false);
        return;
      }

      const token = localStorage.getItem("token");

            try {
                // Fetch Users
                const usersResponse = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

        if (!usersResponse.ok) {
          throw new Error("Failed to fetch users");
        }

        const usersData = await usersResponse.json();
        // console.log('EditTeam Users data:', usersData);
        setUsers(
          usersData.map((user) => ({
            id: user.id,
            fullName: `${(user.fname || "").trim()} ${(
              user.lname || ""
            ).trim()}`.trim(),
            fname: user.fname,
            lname: user.lname,
            roles: Array.isArray(user.roles)
              ? user.roles.map((r) => (r.name || "").trim())
              : [],
            resource_types: Array.isArray(user.resource_types)
              ? user.resource_types.map((rt) => (rt.name || "").trim())
              : [],
          }))
        );

                // Fetch Departments
                const departmentsResponse = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

        if (!departmentsResponse.ok) {
          throw new Error("Failed to fetch departments");
        }

        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData.departments);

                // Fetch Team Details if editing an existing team
                if (dataItemsId) {
                    const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

          if (!teamResponse.ok) {
            throw new Error("Failed to fetch team details");
          }

          const teamData = await teamResponse.json();

          // Set team data to state
          setTeam(teamData.team);

          // Set values for form fields based on teamData
          setTeamName(teamData.team.name || ""); 
          setIcon(teamData.team.icon || null); 
          setLogo(teamData.team.logo || null); 
          setPoc(teamData.team.poc || ""); 
          setManager(teamData.team.manager || ""); 
          setTeamLead(teamData.team.team_lead || ""); 
          // Handle workday - it comes as JSON string from backend
          let workdayArray = [];
          if (teamData.team.workday) {
            try {
              workdayArray = typeof teamData.team.workday === 'string'
                ? JSON.parse(teamData.team.workday)
                : teamData.team.workday;
            } catch (e) {
              console.error('Error parsing workday:', e);
              workdayArray = [];
            }
          }
          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);
          setBillableHours(teamData.team.billable_hours || ""); 
          setLaunch(teamData.team.launch || ""); 
          setExistingIcon(teamData.team.icon || "");
          setExistingLogo(teamData.team.logo || "");

          // Set departmentId by accessing the first department's ID if available
          const departmentId =
            teamData.team.departments && teamData.team.departments.length > 0
              ? teamData.team.departments[0].id
              : "";
          setDepartmentId(departmentId);

          // Set React Select default values after data is loaded
          setTimeout(() => {
            if (departmentId) {
              const deptOption = departmentsData.departments.find(d => d.id === departmentId);
              if (deptOption) {
                setSelectedDepartment({ value: deptOption.id, label: deptOption.name });
              }
            }

            if (teamData.team.poc) {
              setSelectedPoc({ value: teamData.team.poc, label: teamData.team.poc });
            }

            if (teamData.team.manager) {
              setSelectedManager({ value: teamData.team.manager, label: teamData.team.manager });
            }

            if (teamData.team.team_lead) {
              setSelectedTeamLead({ value: teamData.team.team_lead, label: teamData.team.team_lead });
            }
          }, 100); 
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [dataItemsId]);

  // Fetch logged-in user data (user_id)
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      setLoggedInUser(userId);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setError(""); // Clear any previous error

    // Get user_id from localStorage for 'updated_by'
    const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Fetch the logged-in user's data for full name
            const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!loggedUserResponse.ok) {
                throw new Error('Failed to fetch logged-in user data');
            }
    
            const loggedUserData = await loggedUserResponse.json();
            const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`; 
            // console.log('Logged-in User Full Name:', fullName); 
    
            // Prepare the data object (without file data)
            const requestData = {
                name: teamName.trim(),
                department_id: parseInt(departmentId),
                launch: launch,
                workday: workday,
                billable_hours: billableHours ? parseInt(billableHours) : null,
                poc: poc,
                manager: manager,
                team_lead: teamLead,
                updated_by: updatedBy,
            };
    
            // Convert icon and logo files to Base64 if they are provided
            if (icon && icon instanceof File) { // Check if it's a valid File
                const iconBase64 = await convertToBase64(icon);
                requestData.icon = iconBase64;
            } else {
                console.log("No valid icon file selected.");
            }
    
            if (logo && logo instanceof File) { // Check if it's a valid File
                const logoBase64 = await convertToBase64(logo);
                requestData.logo = logoBase64;
            } else {
                console.log("No valid logo file selected.");
            }
    
            // Log the final request data before submission
            console.log("Request data before submission:", requestData);
    
            // Make the PUT request to update the team with JSON data
            const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData), // Send data as JSON
            });
    
            if (!response.ok) {
                throw new Error('Failed to update team: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Team "${result.name || teamName}" updated successfully!`);
            alertMessage('success');

              
        // Invalidate the 'Team' tag to trigger a refetch of the team list
        dispatch(teamApi.util.invalidateTags(['Team'])); 
       

    
            // Optionally, close the modal after success
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000); 
            
            navigate('/settings');
        } catch (error) {
            alertMessage('error');
        }
    };
    
    // Helper function to convert a file to Base64
    const convertToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            if (file instanceof File) {  
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result); 
                reader.onerror = reject;
                reader.readAsDataURL(file); 
            } else {
                reject('The provided object is not a valid File.');
            }
        });
    };
    

  const handleClose = () => {
    setVisible(false);
    navigate("/settings");
  };



  
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500">
        {/* Header - Responsive */}
        <div className="bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                <Users className="w-4 h-4 sm:w-6 sm:h-6" />
              </div>
              <div>
                <div className="text-left">
                  <h2 className="text-lg sm:text-xl font-semibold">
                    Edit Team
                  </h2>
                  <p className="text-white-100 text-xs sm:text-sm">
                    Update team information
                  </p>
                </div>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </div>
        {/* Form Content - Responsive */}
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]">
          {/* Error Message - Responsive */}
          {formError && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2">
              <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-left font-medium text-red-800 text-sm sm:text-base">
                  Action Required
                </p>
                <p className="text-red-700 text-xs sm:text-sm">{formError}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Responsive Grid Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Department & Team Info Section */}
              <div className="space-y-3 sm:space-y-4">
                <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                    <Building className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                    <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                      Organization
                    </h3>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Department <span className="text-red-500">*</span>
                      </label>
                      <Select
                        options={departmentOptions}
                        value={selectedDepartment}
                        onChange={(option) => {
                          setSelectedDepartment(option);
                          setDepartmentId(option?.value || '');
                        }}
                        placeholder="Select Department"
                        className="w-full"
                        isSearchable
                        styles={{
                          control: (base, state) => ({
                            ...base,
                            borderRadius: '0.5rem',
                            borderWidth: '2px',
                            borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                            boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                            '&:hover': {
                              borderColor: '#D1D5DB'
                            }
                          })
                        }}
                      />
                    </div>

                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Team Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={teamName}
                        onChange={(e) => setTeamName(e.target.value)}
                        onFocus={() => setFocusedField("teamName")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="Enter team name"
                        className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                          focusedField === "teamName"
                            ? "border-primary-500 shadow-lg shadow-primary/10-100"
                            : "border-gray-200 focus:border-primary hover:border-gray-300"
                        }`}
                      />
                    </div>
                  </div>
                </div>
              </div>

                {/* POC */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="poc"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    POC
                  </label>
                  <Select
                    options={pocOptions}
                    value={selectedPoc}
                    onChange={(option) => {
                      setSelectedPoc(option);
                      setPoc(option?.value || '');
                    }}
                    placeholder="Select POC"
                    className="w-full"
                    isSearchable
                    noOptionsMessage={() => "No options available"}
                  />
                </div>

                {/* Manager */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="manager"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Manager
                  </label>
                  <Select
                    options={managerOptions}
                    value={selectedManager}
                    onChange={(option) => {
                      setSelectedManager(option);
                      setManager(option?.value || '');
                    }}
                    placeholder="Select Manager"
                    className="w-full"
                    isSearchable
                    noOptionsMessage={() => "No managers found"}
                  />
                </div>

                {/* Team Lead */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="teamLead"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Team Lead
                  </label>
                  {/* <select
                    id="teamLead"
                    value={teamLead}
                    onChange={(e) => setTeamLead(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Select Team Lead</option>
                    {users
                      .filter((u) =>
                        u.roles?.some((r) =>
                          [
                            "team-lead",
                            "manager",
                            "hod",
                            "admin",
                            "super-admin",
                          ].includes(r)
                        )
                      )
                      .map((u) => (
                        <option key={u.id} value={u.fullName}>
                          {u.fullName}
                        </option>
                      ))}
                  </select> */}
                   <Select
      options={teamLeadOptions}
      value={selectedTeamLead}
      onChange={(option) => {
        setSelectedTeamLead(option);
        setTeamLead(option?.value || '');
      }}
      placeholder="Select Team Lead"
      className="w-full"
      isSearchable
    />
                </div>

                {/* Workday */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label className="block text-sm font-medium text-gray-700 pb-4">
                    Work Days *
                  </label>
                  <Select
                    isMulti
                    options={daysOfWeek}
                    value={workday.map(day => ({ value: day, label: day }))}
                    onChange={handleWorkdayChange}
                    placeholder="Select Work Days"
                    className="w-full"
                    isDisabled={loading}
                    noOptionsMessage={() => "No options available"}
                  />
                  {workday.length > 0 && (
                    <p className="text-xs text-gray-500 mt-2">
                      Selected: {workday.join(", ")}
                    </p>
                  )}
                </div>

                {/* Billable Hours */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="billableHours"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Billable Hours
                  </label>
                  <input
                    type="number"
                    id="billableHours"
                    value={billableHours}
                    onChange={(e) => setBillableHours(e.target.value)}
                    min="0"
                    placeholder="Enter billable hours"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>

                {/* Launch */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="launch"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Launch Date
                  </label>
                  <input
                    type="date"
                    id="launch"
                    value={launch}
                    onChange={(e) => setLaunch(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="mb-4 w-full sm:w-1/2 px-4 opacity-0"></div>

                {/* Icon Preview */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="icon"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Icon
                  </label>
                  {icon && icon instanceof File ? (
                    <>
                      <img
                        src={URL.createObjectURL(icon)}
                        alt="Icon Preview"
                        className="w-20 h-20 mb-2"
                      />
                      <input
                        type="file"
                        id="icon"
                        onChange={(e) => setIcon(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  ) : (
                    <>
                      {(existingIcon || icon) && (
                        <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                          <img
                            // Prefer stored path when string
                            src={
                              typeof icon === "string" && icon
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}`
                                : existingIcon
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}`
                                : ""
                            }
                            alt="Icon Preview"
                            className="w-auto h-auto object-cover"
                          />
                        </div>
                      )}
                      <input
                        type="file"
                        id="icon"
                        onChange={(e) => setIcon(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  )}
                </div>

                {/* Logo Preview */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="logo"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Logo
                  </label>
                  {logo && logo instanceof File ? (
                    <>
                      <img
                        src={URL.createObjectURL(logo)}
                        alt="Logo Preview"
                        className="w-20 h-20 mb-2"
                      />
                      <input
                        type="file"
                        id="logo"
                        onChange={(e) => setLogo(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  ) : (
                    <>
                      {(existingLogo || logo) && (
                        <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                          <img
                            // Prefer stored path when string
                            src={
                              typeof logo === "string" && logo
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}`
                                : existingLogo
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}`
                                : ""
                            }
                            alt="Logo Preview"
                            className="w-auto h-auto object-cover"
                          />
                        </div>
                      )}
                      <input
                        type="file"
                        id="logo"
                        onChange={(e) => setLogo(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  )}
                </div>
              </div>
              <div className="text-left pt-6">
                <button
                  type="submit"
                  className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                >
                  <span class="material-symbols-rounded text-white text-xl font-regular">
                    add_circle
                  </span>
                  {loading ? "Updating..." : "Update Team"}
                </button>
              </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditTeam;
