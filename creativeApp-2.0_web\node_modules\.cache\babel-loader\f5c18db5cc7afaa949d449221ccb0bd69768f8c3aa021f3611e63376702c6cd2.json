{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\EditTeam.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { alertMessage } from \"../../common/coreui\";\nimport Select from \"react-select\";\nimport { useDispatch } from 'react-redux';\nimport { teamApi } from './../../features/api';\nimport { X, Users, Building, User, Calendar, Clock, Upload, AlertCircle, CheckCircle } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst isTokenValid = () => {\n  const token = localStorage.getItem(\"token\");\n  return token !== null;\n};\nconst EditTeam = ({\n  dataItemsId,\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [team, setTeam] = useState(null);\n  const [teamName, setTeamName] = useState(\"\");\n  const [icon, setIcon] = useState(null);\n  const [existingIcon, setExistingIcon] = useState(\"\");\n  const [logo, setLogo] = useState(null);\n  const [existingLogo, setExistingLogo] = useState(\"\");\n  const [poc, setPoc] = useState(\"\");\n  const [manager, setManager] = useState(\"\");\n  const [teamLead, setTeamLead] = useState(\"\");\n  const [workday, setWorkday] = useState([]);\n  const [billableHours, setBillableHours] = useState(\"\");\n  const [dailyBillableHours, setDailyBillableHours] = useState(\"\");\n  const [weeklyBillableHours, setWeeklyBillableHours] = useState(\"\");\n  const [launch, setLaunch] = useState(\"\");\n  const [departmentId, setDepartmentId] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [focusedField, setFocusedField] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(\"\");\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // React Select states\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\n  const [selectedPoc, setSelectedPoc] = useState(null);\n  const [selectedManager, setSelectedManager] = useState(null);\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\n\n  // Create options for React Select dropdowns\n  const departmentOptions = departments.map(dept => ({\n    value: dept.id,\n    label: dept.name\n  }));\n\n  // Filter based on Responsibility Level (resource_types) instead of roles\n  const pocOptions = users.filter(user => {\n    var _user$resource_types;\n    const hasValidResponsibility = (_user$resource_types = user.resource_types) === null || _user$resource_types === void 0 ? void 0 : _user$resource_types.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n  const managerOptions = users.filter(user => {\n    var _user$resource_types2;\n    const hasValidResponsibility = (_user$resource_types2 = user.resource_types) === null || _user$resource_types2 === void 0 ? void 0 : _user$resource_types2.some(rt => ['Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n  const teamLeadOptions = users.filter(user => {\n    var _user$resource_types3;\n    const hasValidResponsibility = (_user$resource_types3 = user.resource_types) === null || _user$resource_types3 === void 0 ? void 0 : _user$resource_types3.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n\n  // Debug logs for all options\n  console.log('EditTeam - Total users:', users.length);\n  console.log('EditTeam - Manager options:', managerOptions.length);\n  console.log('EditTeam - POC options:', pocOptions.length);\n  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);\n\n  // Days of the week for multi-select\n  const daysOfWeek = [{\n    value: 'Monday',\n    label: 'Monday'\n  }, {\n    value: 'Tuesday',\n    label: 'Tuesday'\n  }, {\n    value: 'Wednesday',\n    label: 'Wednesday'\n  }, {\n    value: 'Thursday',\n    label: 'Thursday'\n  }, {\n    value: 'Friday',\n    label: 'Friday'\n  }, {\n    value: 'Saturday',\n    label: 'Saturday'\n  }, {\n    value: 'Sunday',\n    label: 'Sunday'\n  }];\n\n  // Handle workday selection\n  const handleWorkdayChange = selectedOptions => {\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\n    setWorkday(selectedValues);\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!isTokenValid()) {\n        setError(\"No authentication token found.\");\n        setLoading(false);\n        return;\n      }\n      const token = localStorage.getItem(\"token\");\n      try {\n        // Fetch Users\n        const usersResponse = await fetch(`${API_URL}/users`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!usersResponse.ok) {\n          throw new Error(\"Failed to fetch users\");\n        }\n        const usersData = await usersResponse.json();\n        // console.log('EditTeam Users data:', usersData);\n        setUsers(usersData.map(user => ({\n          id: user.id,\n          fullName: `${(user.fname || \"\").trim()} ${(user.lname || \"\").trim()}`.trim(),\n          fname: user.fname,\n          lname: user.lname,\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || \"\").trim()) : [],\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || \"\").trim()) : []\n        })));\n\n        // Fetch Departments\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!departmentsResponse.ok) {\n          throw new Error(\"Failed to fetch departments\");\n        }\n        const departmentsData = await departmentsResponse.json();\n        setDepartments(departmentsData.departments);\n\n        // Fetch Team Details if editing an existing team\n        if (dataItemsId) {\n          const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          if (!teamResponse.ok) {\n            throw new Error(\"Failed to fetch team details\");\n          }\n          const teamData = await teamResponse.json();\n\n          // Set team data to state\n          setTeam(teamData.team);\n\n          // Set values for form fields based on teamData\n          setTeamName(teamData.team.name || \"\");\n          setIcon(teamData.team.icon || null);\n          setLogo(teamData.team.logo || null);\n          setPoc(teamData.team.poc || \"\");\n          setManager(teamData.team.manager || \"\");\n          setTeamLead(teamData.team.team_lead || \"\");\n          // Handle workday - it comes as JSON string from backend\n          let workdayArray = [];\n          if (teamData.team.workday) {\n            try {\n              workdayArray = typeof teamData.team.workday === 'string' ? JSON.parse(teamData.team.workday) : teamData.team.workday;\n            } catch (e) {\n              console.error('Error parsing workday:', e);\n              workdayArray = [];\n            }\n          }\n          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);\n          setBillableHours(teamData.team.billable_hours || \"\");\n          setLaunch(teamData.team.launch || \"\");\n          setExistingIcon(teamData.team.icon || \"\");\n          setExistingLogo(teamData.team.logo || \"\");\n\n          // Set departmentId by accessing the first department's ID if available\n          const departmentId = teamData.team.departments && teamData.team.departments.length > 0 ? teamData.team.departments[0].id : \"\";\n          setDepartmentId(departmentId);\n\n          // Set React Select default values after data is loaded\n          setTimeout(() => {\n            if (departmentId) {\n              const deptOption = departmentsData.departments.find(d => d.id === departmentId);\n              if (deptOption) {\n                setSelectedDepartment({\n                  value: deptOption.id,\n                  label: deptOption.name\n                });\n              }\n            }\n            if (teamData.team.poc) {\n              setSelectedPoc({\n                value: teamData.team.poc,\n                label: teamData.team.poc\n              });\n            }\n            if (teamData.team.manager) {\n              setSelectedManager({\n                value: teamData.team.manager,\n                label: teamData.team.manager\n              });\n            }\n            if (teamData.team.team_lead) {\n              setSelectedTeamLead({\n                value: teamData.team.team_lead,\n                label: teamData.team.team_lead\n              });\n            }\n          }, 100);\n        }\n      } catch (error) {\n        setError(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [dataItemsId]);\n\n  // Fetch logged-in user data (user_id)\n  useEffect(() => {\n    const userId = localStorage.getItem(\"user_id\");\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setError(\"\"); // Clear any previous error\n\n    // Get user_id from localStorage for 'updated_by'\n    const updatedBy = loggedInUser;\n    if (!updatedBy) {\n      setError('User is not logged in.');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setError('Authentication token is missing.');\n        return;\n      }\n\n      // Fetch the logged-in user's data for full name\n      const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!loggedUserResponse.ok) {\n        throw new Error('Failed to fetch logged-in user data');\n      }\n      const loggedUserData = await loggedUserResponse.json();\n      const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`;\n      // console.log('Logged-in User Full Name:', fullName); \n\n      // Prepare the data object (without file data)\n      const requestData = {\n        name: teamName.trim(),\n        department_id: parseInt(departmentId),\n        launch: launch,\n        workday: workday,\n        billable_hours: billableHours ? parseInt(billableHours) : null,\n        poc: poc,\n        manager: manager,\n        team_lead: teamLead,\n        updated_by: updatedBy\n      };\n\n      // Convert icon and logo files to Base64 if they are provided\n      if (icon && icon instanceof File) {\n        // Check if it's a valid File\n        const iconBase64 = await convertToBase64(icon);\n        requestData.icon = iconBase64;\n      } else {\n        console.log(\"No valid icon file selected.\");\n      }\n      if (logo && logo instanceof File) {\n        // Check if it's a valid File\n        const logoBase64 = await convertToBase64(logo);\n        requestData.logo = logoBase64;\n      } else {\n        console.log(\"No valid logo file selected.\");\n      }\n\n      // Log the final request data before submission\n      console.log(\"Request data before submission:\", requestData);\n\n      // Make the PUT request to update the team with JSON data\n      const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestData) // Send data as JSON\n      });\n      if (!response.ok) {\n        throw new Error('Failed to update team: ' + response.statusText);\n      }\n      const result = await response.json();\n      //setSuccessMessage(`Team \"${result.name || teamName}\" updated successfully!`);\n      alertMessage('success');\n\n      // Invalidate the 'Team' tag to trigger a refetch of the team list\n      dispatch(teamApi.util.invalidateTags(['Team']));\n\n      // Optionally, close the modal after success\n      setTimeout(() => {\n        setVisible(false);\n        setSuccessMessage('');\n      }, 2000);\n      navigate('/settings');\n    } catch (error) {\n      alertMessage('error');\n    }\n  };\n\n  // Helper function to convert a file to Base64\n  const convertToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      if (file instanceof File) {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(file);\n      } else {\n        reject('The provided object is not a valid File.');\n      }\n    });\n  };\n  const handleClose = () => {\n    setVisible(false);\n    navigate(\"/settings\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md w-full max-w-3xl relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4 bg-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl text-left font-medium text-gray-800 px-6 py-4\",\n            children: \"Edit Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-3xl text-gray-500 hover:text-gray-800\",\n            onClick: handleClose,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"text-left p-2 overflow-y-auto max-h-[90vh] scrollbar-vertical px-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"department\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: departmentOptions,\n                value: selectedDepartment,\n                onChange: option => {\n                  setSelectedDepartment(option);\n                  setDepartmentId((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select Department\",\n                className: \"w-full\",\n                isSearchable: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"teamName\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Team Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"teamName\",\n                value: teamName,\n                onChange: e => setTeamName(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"poc\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"POC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: pocOptions,\n                value: selectedPoc,\n                onChange: option => {\n                  setSelectedPoc(option);\n                  setPoc((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select POC\",\n                className: \"w-full\",\n                isSearchable: true,\n                noOptionsMessage: () => \"No options available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"manager\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Manager\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: managerOptions,\n                value: selectedManager,\n                onChange: option => {\n                  setSelectedManager(option);\n                  setManager((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select Manager\",\n                className: \"w-full\",\n                isSearchable: true,\n                noOptionsMessage: () => \"No managers found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"teamLead\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Team Lead\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: teamLeadOptions,\n                value: selectedTeamLead,\n                onChange: option => {\n                  setSelectedTeamLead(option);\n                  setTeamLead((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select Team Lead\",\n                className: \"w-full\",\n                isSearchable: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Work Days *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                isMulti: true,\n                options: daysOfWeek,\n                value: workday.map(day => ({\n                  value: day,\n                  label: day\n                })),\n                onChange: handleWorkdayChange,\n                placeholder: \"Select Work Days\",\n                className: \"w-full\",\n                isDisabled: loading,\n                noOptionsMessage: () => \"No options available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), workday.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-2\",\n                children: [\"Selected: \", workday.join(\", \")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"billableHours\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Billable Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"billableHours\",\n                value: billableHours,\n                onChange: e => setBillableHours(e.target.value),\n                min: \"0\",\n                placeholder: \"Enter billable hours\",\n                className: \"w-full p-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"launch\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Launch Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                id: \"launch\",\n                value: launch,\n                onChange: e => setLaunch(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4 opacity-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"icon\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), icon && icon instanceof File ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(icon),\n                  alt: \"Icon Preview\",\n                  className: \"w-20 h-20 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"icon\",\n                  onChange: e => setIcon(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [(existingIcon || icon) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    // Prefer stored path when string\n                    src: typeof icon === \"string\" && icon ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}` : existingIcon ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}` : \"\",\n                    alt: \"Icon Preview\",\n                    className: \"w-auto h-auto object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"icon\",\n                  onChange: e => setIcon(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this), logo && logo instanceof File ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(logo),\n                  alt: \"Logo Preview\",\n                  className: \"w-20 h-20 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"logo\",\n                  onChange: e => setLogo(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [(existingLogo || logo) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    // Prefer stored path when string\n                    src: typeof logo === \"string\" && logo ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}` : existingLogo ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}` : \"\",\n                    alt: \"Logo Preview\",\n                    className: \"w-auto h-auto object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"logo\",\n                  onChange: e => setLogo(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left pt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"material-symbols-rounded text-white text-xl font-regular\",\n                children: \"add_circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), loading ? \"Updating...\" : \"Update Team\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(EditTeam, \"/L3MhHHzvc49EHnsWc0vinQzylU=\", false, function () {\n  return [useDispatch, useNavigate];\n});\n_c = EditTeam;\nexport default EditTeam;\nvar _c;\n$RefreshReg$(_c, \"EditTeam\");", "map": {"version": 3, "names": ["useEffect", "useState", "useNavigate", "alertMessage", "Select", "useDispatch", "teamApi", "X", "Users", "Building", "User", "Calendar", "Clock", "Upload", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "EditTeam", "dataItemsId", "isVisible", "setVisible", "_s", "dispatch", "navigate", "users", "setUsers", "departments", "setDepartments", "team", "setTeam", "teamName", "setTeamName", "icon", "setIcon", "existingIcon", "setExistingIcon", "logo", "set<PERSON><PERSON>", "existingLogo", "setExistingLogo", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "workday", "setWorkday", "billableHours", "setBillableHours", "dailyBillableHours", "setDailyBillableHours", "weeklyBillableHours", "setWeeklyBillableHours", "launch", "setLaunch", "departmentId", "setDepartmentId", "error", "setError", "errors", "setErrors", "formError", "setFormError", "focusedField", "setFocusedField", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "selectedDepartment", "setSelectedDepartment", "selectedPoc", "setSelectedPoc", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedManager", "selectedTeamLead", "setSelectedTeamLead", "departmentOptions", "map", "dept", "value", "id", "label", "name", "pocOptions", "filter", "user", "_user$resource_types", "hasValidResponsibility", "resource_types", "some", "rt", "includes", "hasValidName", "fullName", "trim", "managerOptions", "_user$resource_types2", "teamLeadOptions", "_user$resource_types3", "console", "log", "length", "daysOfWeek", "handleWorkdayChange", "selectedOptions", "<PERSON><PERSON><PERSON><PERSON>", "option", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "fname", "lname", "roles", "Array", "isArray", "r", "departmentsResponse", "departmentsData", "teamResponse", "teamData", "team_lead", "workdayArray", "JSON", "parse", "e", "billable_hours", "setTimeout", "deptOption", "find", "d", "message", "userId", "handleSubmit", "event", "preventDefault", "updatedBy", "loggedUserResponse", "loggedUserData", "requestData", "department_id", "parseInt", "updated_by", "File", "iconBase64", "convertToBase64", "logoBase64", "response", "body", "stringify", "statusText", "result", "util", "invalidateTags", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onloadend", "onerror", "readAsDataURL", "handleClose", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "options", "onChange", "placeholder", "isSearchable", "type", "target", "required", "noOptionsMessage", "is<PERSON><PERSON><PERSON>", "day", "isDisabled", "join", "min", "src", "URL", "createObjectURL", "alt", "files", "REACT_APP_BASE_STORAGE_URL", "class", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team/EditTeam.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { alertMessage } from \"../../common/coreui\";\r\nimport Select from \"react-select\";\r\nimport { useDispatch } from 'react-redux';\r\nimport { teamApi } from './../../features/api';\r\nimport {\r\n  X,\r\n  Users,\r\n  Building,\r\n  User,\r\n  Calendar,\r\n  Clock,\r\n  Upload,\r\n  AlertCircle,\r\n  CheckCircle,\r\n} from \"lucide-react\";\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem(\"token\");\r\n  return token !== null;\r\n};\r\n\r\nconst EditTeam = ({ dataItemsId, isVisible, setVisible }) => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [departments, setDepartments] = useState([]);\r\n  const [team, setTeam] = useState(null);\r\n  const [teamName, setTeamName] = useState(\"\");\r\n  const [icon, setIcon] = useState(null);\r\n  const [existingIcon, setExistingIcon] = useState(\"\");\r\n  const [logo, setLogo] = useState(null);\r\n  const [existingLogo, setExistingLogo] = useState(\"\");\r\n  const [poc, setPoc] = useState(\"\");\r\n  const [manager, setManager] = useState(\"\");\r\n  const [teamLead, setTeamLead] = useState(\"\");\r\n  const [workday, setWorkday] = useState([]);\r\n  const [billableHours, setBillableHours] = useState(\"\");\r\n  const [dailyBillableHours, setDailyBillableHours] = useState(\"\");\r\n  const [weeklyBillableHours, setWeeklyBillableHours] = useState(\"\");\r\n  const [launch, setLaunch] = useState(\"\");\r\n  const [departmentId, setDepartmentId] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [errors, setErrors] = useState({});\r\n  const [formError, setFormError] = useState(\"\");\r\n  const [focusedField, setFocusedField] = useState(null);\r\n  const [successMessage, setSuccessMessage] = useState(\"\");\r\n  const [loggedInUser, setLoggedInUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // React Select states\r\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\r\n  const [selectedPoc, setSelectedPoc] = useState(null);\r\n  const [selectedManager, setSelectedManager] = useState(null);\r\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\r\n\r\n  // Create options for React Select dropdowns\r\n  const departmentOptions = departments.map(dept => ({\r\n    value: dept.id,\r\n    label: dept.name\r\n  }));\r\n\r\n  // Filter based on Responsibility Level (resource_types) instead of roles\r\n  const pocOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  const managerOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  const teamLeadOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  // Debug logs for all options\r\n  console.log('EditTeam - Total users:', users.length);\r\n  console.log('EditTeam - Manager options:', managerOptions.length);\r\n  console.log('EditTeam - POC options:', pocOptions.length);\r\n  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);\r\n\r\n\r\n  // Days of the week for multi-select\r\n  const daysOfWeek = [\r\n    { value: 'Monday', label: 'Monday' },\r\n    { value: 'Tuesday', label: 'Tuesday' },\r\n    { value: 'Wednesday', label: 'Wednesday' },\r\n    { value: 'Thursday', label: 'Thursday' },\r\n    { value: 'Friday', label: 'Friday' },\r\n    { value: 'Saturday', label: 'Saturday' },\r\n    { value: 'Sunday', label: 'Sunday' }\r\n  ];\r\n\r\n  // Handle workday selection\r\n  const handleWorkdayChange = (selectedOptions) => {\r\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\r\n    setWorkday(selectedValues);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!isTokenValid()) {\r\n        setError(\"No authentication token found.\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      const token = localStorage.getItem(\"token\");\r\n\r\n            try {\r\n                // Fetch Users\r\n                const usersResponse = await fetch(`${API_URL}/users`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n        if (!usersResponse.ok) {\r\n          throw new Error(\"Failed to fetch users\");\r\n        }\r\n\r\n        const usersData = await usersResponse.json();\r\n        // console.log('EditTeam Users data:', usersData);\r\n        setUsers(\r\n          usersData.map((user) => ({\r\n            id: user.id,\r\n            fullName: `${(user.fname || \"\").trim()} ${(\r\n              user.lname || \"\"\r\n            ).trim()}`.trim(),\r\n            fname: user.fname,\r\n            lname: user.lname,\r\n            roles: Array.isArray(user.roles)\r\n              ? user.roles.map((r) => (r.name || \"\").trim())\r\n              : [],\r\n            resource_types: Array.isArray(user.resource_types)\r\n              ? user.resource_types.map((rt) => (rt.name || \"\").trim())\r\n              : [],\r\n          }))\r\n        );\r\n\r\n                // Fetch Departments\r\n                const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n        if (!departmentsResponse.ok) {\r\n          throw new Error(\"Failed to fetch departments\");\r\n        }\r\n\r\n        const departmentsData = await departmentsResponse.json();\r\n        setDepartments(departmentsData.departments);\r\n\r\n                // Fetch Team Details if editing an existing team\r\n                if (dataItemsId) {\r\n                    const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Authorization': `Bearer ${token}`,\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                    });\r\n\r\n          if (!teamResponse.ok) {\r\n            throw new Error(\"Failed to fetch team details\");\r\n          }\r\n\r\n          const teamData = await teamResponse.json();\r\n\r\n          // Set team data to state\r\n          setTeam(teamData.team);\r\n\r\n          // Set values for form fields based on teamData\r\n          setTeamName(teamData.team.name || \"\"); \r\n          setIcon(teamData.team.icon || null); \r\n          setLogo(teamData.team.logo || null); \r\n          setPoc(teamData.team.poc || \"\"); \r\n          setManager(teamData.team.manager || \"\"); \r\n          setTeamLead(teamData.team.team_lead || \"\"); \r\n          // Handle workday - it comes as JSON string from backend\r\n          let workdayArray = [];\r\n          if (teamData.team.workday) {\r\n            try {\r\n              workdayArray = typeof teamData.team.workday === 'string'\r\n                ? JSON.parse(teamData.team.workday)\r\n                : teamData.team.workday;\r\n            } catch (e) {\r\n              console.error('Error parsing workday:', e);\r\n              workdayArray = [];\r\n            }\r\n          }\r\n          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);\r\n          setBillableHours(teamData.team.billable_hours || \"\"); \r\n          setLaunch(teamData.team.launch || \"\"); \r\n          setExistingIcon(teamData.team.icon || \"\");\r\n          setExistingLogo(teamData.team.logo || \"\");\r\n\r\n          // Set departmentId by accessing the first department's ID if available\r\n          const departmentId =\r\n            teamData.team.departments && teamData.team.departments.length > 0\r\n              ? teamData.team.departments[0].id\r\n              : \"\";\r\n          setDepartmentId(departmentId);\r\n\r\n          // Set React Select default values after data is loaded\r\n          setTimeout(() => {\r\n            if (departmentId) {\r\n              const deptOption = departmentsData.departments.find(d => d.id === departmentId);\r\n              if (deptOption) {\r\n                setSelectedDepartment({ value: deptOption.id, label: deptOption.name });\r\n              }\r\n            }\r\n\r\n            if (teamData.team.poc) {\r\n              setSelectedPoc({ value: teamData.team.poc, label: teamData.team.poc });\r\n            }\r\n\r\n            if (teamData.team.manager) {\r\n              setSelectedManager({ value: teamData.team.manager, label: teamData.team.manager });\r\n            }\r\n\r\n            if (teamData.team.team_lead) {\r\n              setSelectedTeamLead({ value: teamData.team.team_lead, label: teamData.team.team_lead });\r\n            }\r\n          }, 100); \r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [dataItemsId]);\r\n\r\n  // Fetch logged-in user data (user_id)\r\n  useEffect(() => {\r\n    const userId = localStorage.getItem(\"user_id\");\r\n    if (userId) {\r\n      setLoggedInUser(userId);\r\n    }\r\n  }, []);\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n    setError(\"\"); // Clear any previous error\r\n\r\n    // Get user_id from localStorage for 'updated_by'\r\n    const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return;\r\n            }\r\n    \r\n            // Fetch the logged-in user's data for full name\r\n            const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!loggedUserResponse.ok) {\r\n                throw new Error('Failed to fetch logged-in user data');\r\n            }\r\n    \r\n            const loggedUserData = await loggedUserResponse.json();\r\n            const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`; \r\n            // console.log('Logged-in User Full Name:', fullName); \r\n    \r\n            // Prepare the data object (without file data)\r\n            const requestData = {\r\n                name: teamName.trim(),\r\n                department_id: parseInt(departmentId),\r\n                launch: launch,\r\n                workday: workday,\r\n                billable_hours: billableHours ? parseInt(billableHours) : null,\r\n                poc: poc,\r\n                manager: manager,\r\n                team_lead: teamLead,\r\n                updated_by: updatedBy,\r\n            };\r\n    \r\n            // Convert icon and logo files to Base64 if they are provided\r\n            if (icon && icon instanceof File) { // Check if it's a valid File\r\n                const iconBase64 = await convertToBase64(icon);\r\n                requestData.icon = iconBase64;\r\n            } else {\r\n                console.log(\"No valid icon file selected.\");\r\n            }\r\n    \r\n            if (logo && logo instanceof File) { // Check if it's a valid File\r\n                const logoBase64 = await convertToBase64(logo);\r\n                requestData.logo = logoBase64;\r\n            } else {\r\n                console.log(\"No valid logo file selected.\");\r\n            }\r\n    \r\n            // Log the final request data before submission\r\n            console.log(\"Request data before submission:\", requestData);\r\n    \r\n            // Make the PUT request to update the team with JSON data\r\n            const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify(requestData), // Send data as JSON\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update team: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Team \"${result.name || teamName}\" updated successfully!`);\r\n            alertMessage('success');\r\n\r\n              \r\n        // Invalidate the 'Team' tag to trigger a refetch of the team list\r\n        dispatch(teamApi.util.invalidateTags(['Team'])); \r\n       \r\n\r\n    \r\n            // Optionally, close the modal after success\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage('');\r\n            }, 2000); \r\n            \r\n            navigate('/settings');\r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n    \r\n    // Helper function to convert a file to Base64\r\n    const convertToBase64 = (file) => {\r\n        return new Promise((resolve, reject) => {\r\n            if (file instanceof File) {  \r\n                const reader = new FileReader();\r\n                reader.onloadend = () => resolve(reader.result); \r\n                reader.onerror = reject;\r\n                reader.readAsDataURL(file); \r\n            } else {\r\n                reject('The provided object is not a valid File.');\r\n            }\r\n        });\r\n    };\r\n    \r\n\r\n  const handleClose = () => {\r\n    setVisible(false);\r\n    navigate(\"/settings\");\r\n  };\r\n\r\n\r\n\r\n  \r\n  return (\r\n    <>\r\n      {isVisible && (\r\n        <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n          <div className=\"bg-white rounded-lg shadow-md w-full max-w-3xl relative\">\r\n            <div className=\"flex justify-between items-center mb-4 bg-gray-100\">\r\n              <h4 className=\"text-xl text-left font-medium text-gray-800 px-6 py-4\">\r\n                Edit Team\r\n              </h4>\r\n              <button\r\n                className=\"text-3xl text-gray-500 hover:text-gray-800\"\r\n                onClick={handleClose}\r\n              >\r\n                &times;\r\n              </button>\r\n            </div>\r\n            <form\r\n              onSubmit={handleSubmit}\r\n              className=\"text-left p-2 overflow-y-auto max-h-[90vh] scrollbar-vertical px-6\"\r\n            >\r\n              <div className=\"flex flex-wrap items-start justify-between\">\r\n                {/* Department */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"department\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Department\r\n                  </label>\r\n                  <Select\r\n                    options={departmentOptions}\r\n                    value={selectedDepartment}\r\n                    onChange={(option) => {\r\n                      setSelectedDepartment(option);\r\n                      setDepartmentId(option?.value || '');\r\n                    }}\r\n                    placeholder=\"Select Department\"\r\n                    className=\"w-full\"\r\n                    isSearchable\r\n                  />\r\n                </div>\r\n\r\n                {/* Team Name */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"teamName\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Team Name\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"teamName\"\r\n                    value={teamName}\r\n                    onChange={(e) => setTeamName(e.target.value)}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                    required\r\n                  />\r\n                </div>\r\n\r\n                {/* POC */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"poc\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    POC\r\n                  </label>\r\n                  <Select\r\n                    options={pocOptions}\r\n                    value={selectedPoc}\r\n                    onChange={(option) => {\r\n                      setSelectedPoc(option);\r\n                      setPoc(option?.value || '');\r\n                    }}\r\n                    placeholder=\"Select POC\"\r\n                    className=\"w-full\"\r\n                    isSearchable\r\n                    noOptionsMessage={() => \"No options available\"}\r\n                  />\r\n                </div>\r\n\r\n                {/* Manager */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"manager\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Manager\r\n                  </label>\r\n                  <Select\r\n                    options={managerOptions}\r\n                    value={selectedManager}\r\n                    onChange={(option) => {\r\n                      setSelectedManager(option);\r\n                      setManager(option?.value || '');\r\n                    }}\r\n                    placeholder=\"Select Manager\"\r\n                    className=\"w-full\"\r\n                    isSearchable\r\n                    noOptionsMessage={() => \"No managers found\"}\r\n                  />\r\n                </div>\r\n\r\n                {/* Team Lead */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"teamLead\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Team Lead\r\n                  </label>\r\n                  {/* <select\r\n                    id=\"teamLead\"\r\n                    value={teamLead}\r\n                    onChange={(e) => setTeamLead(e.target.value)}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  >\r\n                    <option value=\"\">Select Team Lead</option>\r\n                    {users\r\n                      .filter((u) =>\r\n                        u.roles?.some((r) =>\r\n                          [\r\n                            \"team-lead\",\r\n                            \"manager\",\r\n                            \"hod\",\r\n                            \"admin\",\r\n                            \"super-admin\",\r\n                          ].includes(r)\r\n                        )\r\n                      )\r\n                      .map((u) => (\r\n                        <option key={u.id} value={u.fullName}>\r\n                          {u.fullName}\r\n                        </option>\r\n                      ))}\r\n                  </select> */}\r\n                   <Select\r\n      options={teamLeadOptions}\r\n      value={selectedTeamLead}\r\n      onChange={(option) => {\r\n        setSelectedTeamLead(option);\r\n        setTeamLead(option?.value || '');\r\n      }}\r\n      placeholder=\"Select Team Lead\"\r\n      className=\"w-full\"\r\n      isSearchable\r\n    />\r\n                </div>\r\n\r\n                {/* Workday */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                    Work Days *\r\n                  </label>\r\n                  <Select\r\n                    isMulti\r\n                    options={daysOfWeek}\r\n                    value={workday.map(day => ({ value: day, label: day }))}\r\n                    onChange={handleWorkdayChange}\r\n                    placeholder=\"Select Work Days\"\r\n                    className=\"w-full\"\r\n                    isDisabled={loading}\r\n                    noOptionsMessage={() => \"No options available\"}\r\n                  />\r\n                  {workday.length > 0 && (\r\n                    <p className=\"text-xs text-gray-500 mt-2\">\r\n                      Selected: {workday.join(\", \")}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Billable Hours */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"billableHours\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Billable Hours\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    id=\"billableHours\"\r\n                    value={billableHours}\r\n                    onChange={(e) => setBillableHours(e.target.value)}\r\n                    min=\"0\"\r\n                    placeholder=\"Enter billable hours\"\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Launch */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"launch\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Launch Date\r\n                  </label>\r\n                  <input\r\n                    type=\"date\"\r\n                    id=\"launch\"\r\n                    value={launch}\r\n                    onChange={(e) => setLaunch(e.target.value)}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  />\r\n                </div>\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4 opacity-0\"></div>\r\n\r\n                {/* Icon Preview */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"icon\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Icon\r\n                  </label>\r\n                  {icon && icon instanceof File ? (\r\n                    <>\r\n                      <img\r\n                        src={URL.createObjectURL(icon)}\r\n                        alt=\"Icon Preview\"\r\n                        className=\"w-20 h-20 mb-2\"\r\n                      />\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"icon\"\r\n                        onChange={(e) => setIcon(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {(existingIcon || icon) && (\r\n                        <div className=\"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\">\r\n                          <img\r\n                            // Prefer stored path when string\r\n                            src={\r\n                              typeof icon === \"string\" && icon\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}`\r\n                                : existingIcon\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}`\r\n                                : \"\"\r\n                            }\r\n                            alt=\"Icon Preview\"\r\n                            className=\"w-auto h-auto object-cover\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"icon\"\r\n                        onChange={(e) => setIcon(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Logo Preview */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"logo\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Logo\r\n                  </label>\r\n                  {logo && logo instanceof File ? (\r\n                    <>\r\n                      <img\r\n                        src={URL.createObjectURL(logo)}\r\n                        alt=\"Logo Preview\"\r\n                        className=\"w-20 h-20 mb-2\"\r\n                      />\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"logo\"\r\n                        onChange={(e) => setLogo(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {(existingLogo || logo) && (\r\n                        <div className=\"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\">\r\n                          <img\r\n                            // Prefer stored path when string\r\n                            src={\r\n                              typeof logo === \"string\" && logo\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}`\r\n                                : existingLogo\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}`\r\n                                : \"\"\r\n                            }\r\n                            alt=\"Logo Preview\"\r\n                            className=\"w-auto h-auto object-cover\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"logo\"\r\n                        onChange={(e) => setLogo(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"text-left pt-6\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                >\r\n                  <span class=\"material-symbols-rounded text-white text-xl font-regular\">\r\n                    add_circle\r\n                  </span>\r\n                  {loading ? \"Updating...\" : \"Update Team\"}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default EditTeam;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SACEC,CAAC,EACDC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,MAAMG,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8C,IAAI,EAAEC,OAAO,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkD,GAAG,EAAEC,MAAM,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC8D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,KAAK,EAAEC,QAAQ,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsE,MAAM,EAAEC,SAAS,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgF,OAAO,EAAEC,UAAU,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACkF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM0F,iBAAiB,GAAGtD,WAAW,CAACuD,GAAG,CAACC,IAAI,KAAK;IACjDC,KAAK,EAAED,IAAI,CAACE,EAAE;IACdC,KAAK,EAAEH,IAAI,CAACI;EACd,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,UAAU,GAAG/D,KAAK,CACrBgE,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,oBAAA;IACd,MAAMC,sBAAsB,IAAAD,oBAAA,GAAGD,IAAI,CAACG,cAAc,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBG,IAAI,CAACC,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IACvH,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;EAEL,MAAME,cAAc,GAAG3E,KAAK,CACzBgE,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAW,qBAAA;IACd,MAAMT,sBAAsB,IAAAS,qBAAA,GAAGX,IAAI,CAACG,cAAc,cAAAQ,qBAAA,uBAAnBA,qBAAA,CAAqBP,IAAI,CAACC,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IAC1G,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;EAEL,MAAMI,eAAe,GAAG7E,KAAK,CAC1BgE,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAa,qBAAA;IACd,MAAMX,sBAAsB,IAAAW,qBAAA,GAAGb,IAAI,CAACG,cAAc,cAAAU,qBAAA,uBAAnBA,qBAAA,CAAqBT,IAAI,CAACC,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IACvH,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;;EAEL;EACAM,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEhF,KAAK,CAACiF,MAAM,CAAC;EACpDF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEL,cAAc,CAACM,MAAM,CAAC;EACjEF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEjB,UAAU,CAACkB,MAAM,CAAC;EACzDF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,eAAe,CAACI,MAAM,CAAC;;EAGpE;EACA,MAAMC,UAAU,GAAG,CACjB;IAAEvB,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEF,KAAK,EAAE,SAAS;IAAEE,KAAK,EAAE;EAAU,CAAC,EACtC;IAAEF,KAAK,EAAE,WAAW;IAAEE,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,CACrC;;EAED;EACA,MAAMsB,mBAAmB,GAAIC,eAAe,IAAK;IAC/C,MAAMC,cAAc,GAAGD,eAAe,GAAGA,eAAe,CAAC3B,GAAG,CAAC6B,MAAM,IAAIA,MAAM,CAAC3B,KAAK,CAAC,GAAG,EAAE;IACzFpC,UAAU,CAAC8D,cAAc,CAAC;EAC5B,CAAC;EAEDxH,SAAS,CAAC,MAAM;IACd,MAAM0H,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAAClG,YAAY,CAAC,CAAC,EAAE;QACnB8C,QAAQ,CAAC,gCAAgC,CAAC;QAC1CY,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMzD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAErC,IAAI;QACA;QACA,MAAMgG,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAGxG,OAAO,QAAQ,EAAE;UAClDyG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUrG,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEV,IAAI,CAACkG,aAAa,CAACI,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5C;QACA9F,QAAQ,CACN6F,SAAS,CAACrC,GAAG,CAAEQ,IAAI,KAAM;UACvBL,EAAE,EAAEK,IAAI,CAACL,EAAE;UACXa,QAAQ,EAAE,GAAG,CAACR,IAAI,CAAC+B,KAAK,IAAI,EAAE,EAAEtB,IAAI,CAAC,CAAC,IAAI,CACxCT,IAAI,CAACgC,KAAK,IAAI,EAAE,EAChBvB,IAAI,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;UACjBsB,KAAK,EAAE/B,IAAI,CAAC+B,KAAK;UACjBC,KAAK,EAAEhC,IAAI,CAACgC,KAAK;UACjBC,KAAK,EAAEC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAACiC,KAAK,CAAC,GAC5BjC,IAAI,CAACiC,KAAK,CAACzC,GAAG,CAAE4C,CAAC,IAAK,CAACA,CAAC,CAACvC,IAAI,IAAI,EAAE,EAAEY,IAAI,CAAC,CAAC,CAAC,GAC5C,EAAE;UACNN,cAAc,EAAE+B,KAAK,CAACC,OAAO,CAACnC,IAAI,CAACG,cAAc,CAAC,GAC9CH,IAAI,CAACG,cAAc,CAACX,GAAG,CAAEa,EAAE,IAAK,CAACA,EAAE,CAACR,IAAI,IAAI,EAAE,EAAEY,IAAI,CAAC,CAAC,CAAC,GACvD;QACN,CAAC,CAAC,CACJ,CAAC;;QAEO;QACA,MAAM4B,mBAAmB,GAAG,MAAMb,KAAK,CAAC,GAAGxG,OAAO,cAAc,EAAE;UAC9DyG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUrG,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEV,IAAI,CAACgH,mBAAmB,CAACV,EAAE,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,MAAMU,eAAe,GAAG,MAAMD,mBAAmB,CAACP,IAAI,CAAC,CAAC;QACxD5F,cAAc,CAACoG,eAAe,CAACrG,WAAW,CAAC;;QAEnC;QACA,IAAIR,WAAW,EAAE;UACb,MAAM8G,YAAY,GAAG,MAAMf,KAAK,CAAC,GAAGxG,OAAO,UAAUS,WAAW,EAAE,EAAE;YAChEgG,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACL,eAAe,EAAE,UAAUrG,KAAK,EAAE;cAClC,cAAc,EAAE;YACpB;UACJ,CAAC,CAAC;UAEZ,IAAI,CAACkH,YAAY,CAACZ,EAAE,EAAE;YACpB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;UACjD;UAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAACT,IAAI,CAAC,CAAC;;UAE1C;UACA1F,OAAO,CAACoG,QAAQ,CAACrG,IAAI,CAAC;;UAEtB;UACAG,WAAW,CAACkG,QAAQ,CAACrG,IAAI,CAAC0D,IAAI,IAAI,EAAE,CAAC;UACrCrD,OAAO,CAACgG,QAAQ,CAACrG,IAAI,CAACI,IAAI,IAAI,IAAI,CAAC;UACnCK,OAAO,CAAC4F,QAAQ,CAACrG,IAAI,CAACQ,IAAI,IAAI,IAAI,CAAC;UACnCK,MAAM,CAACwF,QAAQ,CAACrG,IAAI,CAACY,GAAG,IAAI,EAAE,CAAC;UAC/BG,UAAU,CAACsF,QAAQ,CAACrG,IAAI,CAACc,OAAO,IAAI,EAAE,CAAC;UACvCG,WAAW,CAACoF,QAAQ,CAACrG,IAAI,CAACsG,SAAS,IAAI,EAAE,CAAC;UAC1C;UACA,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAIF,QAAQ,CAACrG,IAAI,CAACkB,OAAO,EAAE;YACzB,IAAI;cACFqF,YAAY,GAAG,OAAOF,QAAQ,CAACrG,IAAI,CAACkB,OAAO,KAAK,QAAQ,GACpDsF,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAACrG,IAAI,CAACkB,OAAO,CAAC,GACjCmF,QAAQ,CAACrG,IAAI,CAACkB,OAAO;YAC3B,CAAC,CAAC,OAAOwF,CAAC,EAAE;cACV/B,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAE4E,CAAC,CAAC;cAC1CH,YAAY,GAAG,EAAE;YACnB;UACF;UACApF,UAAU,CAAC4E,KAAK,CAACC,OAAO,CAACO,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;UAC3DlF,gBAAgB,CAACgF,QAAQ,CAACrG,IAAI,CAAC2G,cAAc,IAAI,EAAE,CAAC;UACpDhF,SAAS,CAAC0E,QAAQ,CAACrG,IAAI,CAAC0B,MAAM,IAAI,EAAE,CAAC;UACrCnB,eAAe,CAAC8F,QAAQ,CAACrG,IAAI,CAACI,IAAI,IAAI,EAAE,CAAC;UACzCO,eAAe,CAAC0F,QAAQ,CAACrG,IAAI,CAACQ,IAAI,IAAI,EAAE,CAAC;;UAEzC;UACA,MAAMoB,YAAY,GAChByE,QAAQ,CAACrG,IAAI,CAACF,WAAW,IAAIuG,QAAQ,CAACrG,IAAI,CAACF,WAAW,CAAC+E,MAAM,GAAG,CAAC,GAC7DwB,QAAQ,CAACrG,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC0D,EAAE,GAC/B,EAAE;UACR3B,eAAe,CAACD,YAAY,CAAC;;UAE7B;UACAgF,UAAU,CAAC,MAAM;YACf,IAAIhF,YAAY,EAAE;cAChB,MAAMiF,UAAU,GAAGV,eAAe,CAACrG,WAAW,CAACgH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAK5B,YAAY,CAAC;cAC/E,IAAIiF,UAAU,EAAE;gBACdhE,qBAAqB,CAAC;kBAAEU,KAAK,EAAEsD,UAAU,CAACrD,EAAE;kBAAEC,KAAK,EAAEoD,UAAU,CAACnD;gBAAK,CAAC,CAAC;cACzE;YACF;YAEA,IAAI2C,QAAQ,CAACrG,IAAI,CAACY,GAAG,EAAE;cACrBmC,cAAc,CAAC;gBAAEQ,KAAK,EAAE8C,QAAQ,CAACrG,IAAI,CAACY,GAAG;gBAAE6C,KAAK,EAAE4C,QAAQ,CAACrG,IAAI,CAACY;cAAI,CAAC,CAAC;YACxE;YAEA,IAAIyF,QAAQ,CAACrG,IAAI,CAACc,OAAO,EAAE;cACzBmC,kBAAkB,CAAC;gBAAEM,KAAK,EAAE8C,QAAQ,CAACrG,IAAI,CAACc,OAAO;gBAAE2C,KAAK,EAAE4C,QAAQ,CAACrG,IAAI,CAACc;cAAQ,CAAC,CAAC;YACpF;YAEA,IAAIuF,QAAQ,CAACrG,IAAI,CAACsG,SAAS,EAAE;cAC3BnD,mBAAmB,CAAC;gBAAEI,KAAK,EAAE8C,QAAQ,CAACrG,IAAI,CAACsG,SAAS;gBAAE7C,KAAK,EAAE4C,QAAQ,CAACrG,IAAI,CAACsG;cAAU,CAAC,CAAC;YACzF;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,CAAC,OAAOxE,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACkF,OAAO,CAAC;MACzB,CAAC,SAAS;QACRrE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7F,WAAW,CAAC,CAAC;;EAEjB;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMwJ,MAAM,GAAG9H,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAI6H,MAAM,EAAE;MACVxE,eAAe,CAACwE,MAAM,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAOC,KAAK,IAAK;IACpCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBrF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd;IACA,MAAMsF,SAAS,GAAG7E,YAAY;IAE1B,IAAI,CAAC6E,SAAS,EAAE;MACZtF,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACJ;IAEA,IAAI;MACA,MAAM7C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACR6C,QAAQ,CAAC,kCAAkC,CAAC;QAC5C;MACJ;;MAEA;MACA,MAAMuF,kBAAkB,GAAG,MAAMjC,KAAK,CAAC,GAAGxG,OAAO,eAAe,EAAE;QAC9DyG,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUrG,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACoI,kBAAkB,CAAC9B,EAAE,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MAC1D;MAEA,MAAM8B,cAAc,GAAG,MAAMD,kBAAkB,CAAC3B,IAAI,CAAC,CAAC;MACtD,MAAMtB,QAAQ,GAAG,GAAGkD,cAAc,CAAC3B,KAAK,IAAI2B,cAAc,CAAC1B,KAAK,EAAE;MAClE;;MAEA;MACA,MAAM2B,WAAW,GAAG;QAChB9D,IAAI,EAAExD,QAAQ,CAACoE,IAAI,CAAC,CAAC;QACrBmD,aAAa,EAAEC,QAAQ,CAAC9F,YAAY,CAAC;QACrCF,MAAM,EAAEA,MAAM;QACdR,OAAO,EAAEA,OAAO;QAChByF,cAAc,EAAEvF,aAAa,GAAGsG,QAAQ,CAACtG,aAAa,CAAC,GAAG,IAAI;QAC9DR,GAAG,EAAEA,GAAG;QACRE,OAAO,EAAEA,OAAO;QAChBwF,SAAS,EAAEtF,QAAQ;QACnB2G,UAAU,EAAEN;MAChB,CAAC;;MAED;MACA,IAAIjH,IAAI,IAAIA,IAAI,YAAYwH,IAAI,EAAE;QAAE;QAChC,MAAMC,UAAU,GAAG,MAAMC,eAAe,CAAC1H,IAAI,CAAC;QAC9CoH,WAAW,CAACpH,IAAI,GAAGyH,UAAU;MACjC,CAAC,MAAM;QACHlD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC/C;MAEA,IAAIpE,IAAI,IAAIA,IAAI,YAAYoH,IAAI,EAAE;QAAE;QAChC,MAAMG,UAAU,GAAG,MAAMD,eAAe,CAACtH,IAAI,CAAC;QAC9CgH,WAAW,CAAChH,IAAI,GAAGuH,UAAU;MACjC,CAAC,MAAM;QACHpD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC/C;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE4C,WAAW,CAAC;;MAE3D;MACA,MAAMQ,QAAQ,GAAG,MAAM3C,KAAK,CAAC,GAAGxG,OAAO,UAAUS,WAAW,EAAE,EAAE;QAC5DgG,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUrG,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB,CAAC;QACD+I,IAAI,EAAEzB,IAAI,CAAC0B,SAAS,CAACV,WAAW,CAAC,CAAE;MACvC,CAAC,CAAC;MAEF,IAAI,CAACQ,QAAQ,CAACxC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAGuC,QAAQ,CAACG,UAAU,CAAC;MACpE;MAEA,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACrC,IAAI,CAAC,CAAC;MACpC;MACA/H,YAAY,CAAC,SAAS,CAAC;;MAG3B;MACA8B,QAAQ,CAAC3B,OAAO,CAACsK,IAAI,CAACC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;;MAI3C;MACA1B,UAAU,CAAC,MAAM;QACbpH,UAAU,CAAC,KAAK,CAAC;QACjB+C,iBAAiB,CAAC,EAAE,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;MAER5C,QAAQ,CAAC,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACZlE,YAAY,CAAC,OAAO,CAAC;IACzB;EACJ,CAAC;;EAED;EACA,MAAMkK,eAAe,GAAIS,IAAI,IAAK;IAC9B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAIH,IAAI,YAAYX,IAAI,EAAE;QACtB,MAAMe,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAACP,MAAM,CAAC;QAC/CO,MAAM,CAACG,OAAO,GAAGJ,MAAM;QACvBC,MAAM,CAACI,aAAa,CAACR,IAAI,CAAC;MAC9B,CAAC,MAAM;QACHG,MAAM,CAAC,0CAA0C,CAAC;MACtD;IACJ,CAAC,CAAC;EACN,CAAC;EAGH,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBxJ,UAAU,CAAC,KAAK,CAAC;IACjBG,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAKD,oBACEjB,OAAA,CAAAE,SAAA;IAAAqK,QAAA,EACG1J,SAAS,iBACRb,OAAA;MAAKwK,SAAS,EAAC,kHAAkH;MAAAD,QAAA,eAC/HvK,OAAA;QAAKwK,SAAS,EAAC,yDAAyD;QAAAD,QAAA,gBACtEvK,OAAA;UAAKwK,SAAS,EAAC,oDAAoD;UAAAD,QAAA,gBACjEvK,OAAA;YAAIwK,SAAS,EAAC,uDAAuD;YAAAD,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5K,OAAA;YACEwK,SAAS,EAAC,4CAA4C;YACtDK,OAAO,EAAEP,WAAY;YAAAC,QAAA,EACtB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5K,OAAA;UACE8K,QAAQ,EAAEtC,YAAa;UACvBgC,SAAS,EAAC,oEAAoE;UAAAD,QAAA,gBAE9EvK,OAAA;YAAKwK,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBAEzDvK,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,YAAY;gBACpBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA,CAACb,MAAM;gBACL6L,OAAO,EAAEtG,iBAAkB;gBAC3BG,KAAK,EAAEX,kBAAmB;gBAC1B+G,QAAQ,EAAGzE,MAAM,IAAK;kBACpBrC,qBAAqB,CAACqC,MAAM,CAAC;kBAC7BrD,eAAe,CAAC,CAAAqD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBACtC,CAAE;gBACFqG,WAAW,EAAC,mBAAmB;gBAC/BV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,UAAU;gBAClBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA;gBACEoL,IAAI,EAAC,MAAM;gBACXtG,EAAE,EAAC,UAAU;gBACbD,KAAK,EAAErD,QAAS;gBAChByJ,QAAQ,EAAGjD,CAAC,IAAKvG,WAAW,CAACuG,CAAC,CAACqD,MAAM,CAACxG,KAAK,CAAE;gBAC7C2F,SAAS,EAAC,8CAA8C;gBACxDc,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,KAAK;gBACbP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA,CAACb,MAAM;gBACL6L,OAAO,EAAE/F,UAAW;gBACpBJ,KAAK,EAAET,WAAY;gBACnB6G,QAAQ,EAAGzE,MAAM,IAAK;kBACpBnC,cAAc,CAACmC,MAAM,CAAC;kBACtBrE,MAAM,CAAC,CAAAqE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBAC7B,CAAE;gBACFqG,WAAW,EAAC,YAAY;gBACxBV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;gBACZI,gBAAgB,EAAEA,CAAA,KAAM;cAAuB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,SAAS;gBACjBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA,CAACb,MAAM;gBACL6L,OAAO,EAAEnF,cAAe;gBACxBhB,KAAK,EAAEP,eAAgB;gBACvB2G,QAAQ,EAAGzE,MAAM,IAAK;kBACpBjC,kBAAkB,CAACiC,MAAM,CAAC;kBAC1BnE,UAAU,CAAC,CAAAmE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBACjC,CAAE;gBACFqG,WAAW,EAAC,gBAAgB;gBAC5BV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;gBACZI,gBAAgB,EAAEA,CAAA,KAAM;cAAoB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,UAAU;gBAClBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eA0BP5K,OAAA,CAACb,MAAM;gBACpB6L,OAAO,EAAEjF,eAAgB;gBACzBlB,KAAK,EAAEL,gBAAiB;gBACxByG,QAAQ,EAAGzE,MAAM,IAAK;kBACpB/B,mBAAmB,CAAC+B,MAAM,CAAC;kBAC3BjE,WAAW,CAAC,CAAAiE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBAClC,CAAE;gBACFqG,WAAW,EAAC,kBAAkB;gBAC9BV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBAAOwK,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA,CAACb,MAAM;gBACLqM,OAAO;gBACPR,OAAO,EAAE5E,UAAW;gBACpBvB,KAAK,EAAErC,OAAO,CAACmC,GAAG,CAAC8G,GAAG,KAAK;kBAAE5G,KAAK,EAAE4G,GAAG;kBAAE1G,KAAK,EAAE0G;gBAAI,CAAC,CAAC,CAAE;gBACxDR,QAAQ,EAAE5E,mBAAoB;gBAC9B6E,WAAW,EAAC,kBAAkB;gBAC9BV,SAAS,EAAC,QAAQ;gBAClBkB,UAAU,EAAE1H,OAAQ;gBACpBuH,gBAAgB,EAAEA,CAAA,KAAM;cAAuB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDpI,OAAO,CAAC2D,MAAM,GAAG,CAAC,iBACjBnG,OAAA;gBAAGwK,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,GAAC,YAC9B,EAAC/H,OAAO,CAACmJ,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,eAAe;gBACvBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA;gBACEoL,IAAI,EAAC,QAAQ;gBACbtG,EAAE,EAAC,eAAe;gBAClBD,KAAK,EAAEnC,aAAc;gBACrBuI,QAAQ,EAAGjD,CAAC,IAAKrF,gBAAgB,CAACqF,CAAC,CAACqD,MAAM,CAACxG,KAAK,CAAE;gBAClD+G,GAAG,EAAC,GAAG;gBACPV,WAAW,EAAC,sBAAsB;gBAClCV,SAAS,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,QAAQ;gBAChBP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5K,OAAA;gBACEoL,IAAI,EAAC,MAAM;gBACXtG,EAAE,EAAC,QAAQ;gBACXD,KAAK,EAAE7B,MAAO;gBACdiI,QAAQ,EAAGjD,CAAC,IAAK/E,SAAS,CAAC+E,CAAC,CAACqD,MAAM,CAACxG,KAAK,CAAE;gBAC3C2F,SAAS,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5K,OAAA;cAAKwK,SAAS,EAAC;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG3D5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,MAAM;gBACdP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPlJ,IAAI,IAAIA,IAAI,YAAYwH,IAAI,gBAC3BlJ,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,gBACEvK,OAAA;kBACE6L,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACrK,IAAI,CAAE;kBAC/BsK,GAAG,EAAC,cAAc;kBAClBxB,SAAS,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACF5K,OAAA;kBACEoL,IAAI,EAAC,MAAM;kBACXtG,EAAE,EAAC,MAAM;kBACTmG,QAAQ,EAAGjD,CAAC,IAAKrG,OAAO,CAACqG,CAAC,CAACqD,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CzB,SAAS,EAAC;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CAAC,gBAEH5K,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,GACG,CAAC3I,YAAY,IAAIF,IAAI,kBACpB1B,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,eACnEvK,OAAA;oBACE;oBACA6L,GAAG,EACD,OAAOnK,IAAI,KAAK,QAAQ,IAAIA,IAAI,GAC5B,GAAGtB,OAAO,CAACC,GAAG,CAAC6L,0BAA0B,IAAIxK,IAAI,EAAE,GACnDE,YAAY,GACZ,GAAGxB,OAAO,CAACC,GAAG,CAAC6L,0BAA0B,IAAItK,YAAY,EAAE,GAC3D,EACL;oBACDoK,GAAG,EAAC,cAAc;oBAClBxB,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eACD5K,OAAA;kBACEoL,IAAI,EAAC,MAAM;kBACXtG,EAAE,EAAC,MAAM;kBACTmG,QAAQ,EAAGjD,CAAC,IAAKrG,OAAO,CAACqG,CAAC,CAACqD,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CzB,SAAS,EAAC;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5K,OAAA;cAAKwK,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCvK,OAAA;gBACE+K,OAAO,EAAC,MAAM;gBACdP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACzD;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACP9I,IAAI,IAAIA,IAAI,YAAYoH,IAAI,gBAC3BlJ,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,gBACEvK,OAAA;kBACE6L,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACjK,IAAI,CAAE;kBAC/BkK,GAAG,EAAC,cAAc;kBAClBxB,SAAS,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACF5K,OAAA;kBACEoL,IAAI,EAAC,MAAM;kBACXtG,EAAE,EAAC,MAAM;kBACTmG,QAAQ,EAAGjD,CAAC,IAAKjG,OAAO,CAACiG,CAAC,CAACqD,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CzB,SAAS,EAAC;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CAAC,gBAEH5K,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,GACG,CAACvI,YAAY,IAAIF,IAAI,kBACpB9B,OAAA;kBAAKwK,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,eACnEvK,OAAA;oBACE;oBACA6L,GAAG,EACD,OAAO/J,IAAI,KAAK,QAAQ,IAAIA,IAAI,GAC5B,GAAG1B,OAAO,CAACC,GAAG,CAAC6L,0BAA0B,IAAIpK,IAAI,EAAE,GACnDE,YAAY,GACZ,GAAG5B,OAAO,CAACC,GAAG,CAAC6L,0BAA0B,IAAIlK,YAAY,EAAE,GAC3D,EACL;oBACDgK,GAAG,EAAC,cAAc;oBAClBxB,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eACD5K,OAAA;kBACEoL,IAAI,EAAC,MAAM;kBACXtG,EAAE,EAAC,MAAM;kBACTmG,QAAQ,EAAGjD,CAAC,IAAKjG,OAAO,CAACiG,CAAC,CAACqD,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CzB,SAAS,EAAC;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5K,OAAA;YAAKwK,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7BvK,OAAA;cACEoL,IAAI,EAAC,QAAQ;cACbZ,SAAS,EAAC,6HAA6H;cAAAD,QAAA,gBAEvIvK,OAAA;gBAAMmM,KAAK,EAAC,0DAA0D;gBAAA5B,QAAA,EAAC;cAEvE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACN5G,OAAO,GAAG,aAAa,GAAG,aAAa;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAAC7J,EAAA,CA5rBIJ,QAAQ;EAAA,QACKvB,WAAW,EACXH,WAAW;AAAA;AAAAmN,EAAA,GAFxBzL,QAAQ;AA8rBd,eAAeA,QAAQ;AAAC,IAAAyL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}