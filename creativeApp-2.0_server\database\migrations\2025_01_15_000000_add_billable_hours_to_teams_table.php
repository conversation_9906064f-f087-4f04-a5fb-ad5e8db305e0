<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('teams', function (Blueprint $table) {
            // Add billable_hours column as integer if it doesn't exist
            if (!Schema::hasColumn('teams', 'billable_hours')) {
                $table->integer('billable_hours')->nullable()->after('workday');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('teams', function (Blueprint $table) {
            if (Schema::hasColumn('teams', 'billable_hours')) {
                $table->dropColumn('billable_hours');
            }
        });
    }
};
