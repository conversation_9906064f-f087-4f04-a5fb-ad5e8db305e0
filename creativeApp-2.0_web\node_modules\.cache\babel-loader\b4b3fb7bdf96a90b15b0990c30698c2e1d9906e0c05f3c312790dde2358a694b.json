{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\TeamDataList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from \"react\";\nimport DataTable from \"react-data-table-component\";\nimport Loading from \"./../../common/Loading\";\nimport { confirmationAlert, Image, ManageColumns, SearchFilter, TableView } from \"./../../common/coreui\";\nimport { useDispatch } from \"react-redux\";\nimport { defaultDateTimeFormat, defaultTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\n\n// Libraries for exporting data to Excel\nimport { saveAs } from \"file-saver\";\nimport * as XLSX from \"xlsx\";\nimport { teamApi, useDeleteTeamMutation, useGetTeamDataQuery, useLazyFetchDataOptionsForTeamQuery } from \"./../../features/api\";\nimport { useNavigate } from \"react-router-dom\";\nimport EditTeam from \"./EditTeam\";\nimport AddTeam from \"./AddTeam\";\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\n\n// API endpoint and configuration constants\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MODULE_NAME = \"Creative Team\";\n\n// Main component for listing Product Type List\nconst TeamDataList = () => {\n  _s();\n  // State variables for data items, filters, search text, modals, and loading status\n  const [filterOptions, setFilterOptions] = useState({});\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\n  const [queryString, setQueryString] = useState(\"\");\n  const [modalVisible, setModalVisible] = useState(false);\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\n  const [dataItemsId, setDataItemsId] = useState(null);\n  const [error, setError] = useState(null);\n  const [viewData, setViewData] = useState(null);\n  const navigate = useNavigate();\n  const [addModalVisible, setAddModalVisible] = useState(false);\n\n  // Sorting and pagination state\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\n  const [sortDirection, setSortDirection] = useState(\"desc\");\n  const [perPage, setPerPage] = useState(\"10\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const {\n    data: dataItems,\n    isFetching,\n    error: fetchError\n  } = useGetTeamDataQuery({\n    sort_by: sortColumn,\n    order: sortDirection,\n    page: currentPage,\n    per_page: perPage,\n    query: queryString\n  });\n  const [triggerFilterByFetch, {\n    data: groupData,\n    error: groupDataError\n  }] = useLazyFetchDataOptionsForTeamQuery();\n  const [deleteTeam] = useDeleteTeamMutation();\n\n  // Build query parameters from selected filters\n  const buildQueryParams = selectedFilters => {\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\n      if (typeof value === \"string\") {\n        return acc + `&${key}=${value}`;\n      }\n      if (Array.isArray(value)) {\n        const vals = value.map(i => i.value).join(\",\");\n        return acc + `&${key}=${vals}`;\n      }\n      return acc;\n    }, \"\");\n    setQueryString(q);\n  };\n  const handleCopy = data => {\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\n    const cleanedData = removeKeys(data, keysToRemove);\n    setViewData(null);\n    setModalVisible(true);\n  };\n  const handleEdit = id => {\n    setViewData(null);\n    setDataItemsId(id);\n    setModalVisible(true);\n  };\n  const handleDelete = id => {\n    confirmationAlert({\n      onConfirm: () => {\n        deleteTeam(id);\n        setViewData(null);\n      }\n    });\n  };\n  let columnSerial = 1;\n  const {\n    rolePermissions\n  } = useRoleBasedAccess();\n\n  // Define columns dynamically based on rolePermissions\n  const [columns, setColumns] = useState(() => [{\n    id: columnSerial++,\n    name: \"Action\",\n    width: \"180px\",\n    className: \"bg-red-300\",\n    cell: item => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-1 mx-2 !min-w-[200px] pl-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => setViewData(item),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"visibility\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleEdit(item.id),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"stylus_note\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 13\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleCopy(item),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"content_copy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 13\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleDelete(item.id),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-sm\",\n          children: \"delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: columnSerial++,\n    name: \"S.No\",\n    selector: (row, index) => (currentPage - 1) * perPage + index + 1,\n    width: \"80px\",\n    omit: false\n  },\n  // {\n  //     id: columnSerial++,\n  //   name: \"Department\",\n  //   selector: (row) => row.department?.name || \"N/A\",\n  //   db_title_field: \"department.name\",\n  //   db_field: \"department_id\",\n  //   sortable: true,\n  //   omit: false,\n  //   filterable: true,\n  // },\n  {\n    id: columnSerial++,\n    name: \"Team Name\",\n    db_field: \"name\",\n    selector: row => row.name || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Point of Contact\",\n    db_field: \"poc\",\n    selector: row => row.poc || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Manager\",\n    db_field: \"manager\",\n    selector: row => row.manager || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Team Lead\",\n    db_field: \"team_lead\",\n    selector: row => row.team_lead || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Work Days\",\n    db_field: \"workday\",\n    selector: row => {\n      if (row.workday) {\n        try {\n          // If workday is a string, parse it as JSON\n          const workdays = typeof row.workday === \"string\" ? JSON.parse(row.workday) : row.workday;\n          return Array.isArray(workdays) ? workdays.join(\", \") : row.workday;\n        } catch (e) {\n          // If parsing fails, return as is\n          return row.workday;\n        }\n      }\n      return \"\";\n    },\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Billable Hours\",\n    db_field: \"billable_hours\",\n    selector: row => row.billable_hours || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Daily Billable Hours\",\n    db_field: \"daily_billable_hours\",\n    selector: row => row.daily_billable_hours || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Weekly Billable Hours\",\n    db_field: \"weekly_billable_hours\",\n    selector: row => row.weekly_billable_hours || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Launch Date\",\n    db_field: \"launch\",\n    selector: row => row.launch || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Team Logo\",\n    width: \"150px\",\n    db_field: \"logo\",\n    selector: row => row !== null && row !== void 0 && row.logo ? \"Has Logo\" : \"No Logo\",\n    cell: row => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex align-middle items-center justify-center px-2 py-2\",\n      children: row !== null && row !== void 0 && row.logo ? /*#__PURE__*/_jsxDEV(Image, {\n        src: `${process.env.REACT_APP_BASE_STORAGE_URL}/${row === null || row === void 0 ? void 0 : row.logo}`,\n        alt: \"Team Logo\",\n        className: \"w-28 rounded-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\",\n        children: [\"No \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 18\n        }, this), \"Logo\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this),\n    sortable: true,\n    omit: false,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Team Icon\",\n    width: \"120px\",\n    db_field: \"icon\",\n    selector: row => row !== null && row !== void 0 && row.icon ? \"Has Icon\" : \"No Icon\",\n    cell: row => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex align-middle items-center justify-center px-2 py-2\",\n      children: row !== null && row !== void 0 && row.icon ? /*#__PURE__*/_jsxDEV(Image, {\n        src: `${process.env.REACT_APP_BASE_STORAGE_URL}/${row === null || row === void 0 ? void 0 : row.icon}`,\n        alt: \"Team Icon\",\n        className: \"w-10 h-10 rounded-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\",\n        children: [\"No \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 18\n        }, this), \"Icon\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 9\n    }, this),\n    sortable: true,\n    omit: false,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Created by\",\n    selector: row => {\n      var _row$creator, _row$creator2;\n      return `${((_row$creator = row.creator) === null || _row$creator === void 0 ? void 0 : _row$creator.fname) || \"\"} ${((_row$creator2 = row.creator) === null || _row$creator2 === void 0 ? void 0 : _row$creator2.lname) || \"\"}`;\n    },\n    db_field: \"created_by\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Created Date\",\n    selector: row => DateTimeFormatDay(row.created_at),\n    db_field: \"created_at\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Created Time\",\n    selector: row => DateTimeFormatHour(row.created_at),\n    db_field: \"created_at\",\n    omit: false,\n    sortable: true,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Updated by\",\n    selector: row => {\n      var _row$updater, _row$updater2;\n      return `${((_row$updater = row.updater) === null || _row$updater === void 0 ? void 0 : _row$updater.fname) || \"\"} ${((_row$updater2 = row.updater) === null || _row$updater2 === void 0 ? void 0 : _row$updater2.lname) || \"\"}`;\n    },\n    db_field: \"updated_by\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Updated Date\",\n    selector: row => DateTimeFormatDay(row.updated_at),\n    db_field: \"updated_at\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Updated Time\",\n    selector: row => DateTimeFormatHour(row.updated_at),\n    db_field: \"updated_at\",\n    omit: false,\n    sortable: true,\n    filterable: false\n  }]);\n  useEffect(() => {\n    setColumns(prevColumns => [...prevColumns.map(col => {\n      if (col.name === \"Action\") {\n        // Update the \"Action\" column dynamically\n        return {\n          ...col,\n          cell: item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-1 mx-2 !min-w-[200px] pl-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => setViewData(item),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"visibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleEdit(item.id),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"stylus_note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleCopy(item),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"content_copy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleDelete(item.id),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm\",\n                children: \"delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this)\n        };\n      }\n      return col;\n    })]);\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\n\n  // Resets the pagination and clear-all filter state\n  const resetPage = () => {\n    if (Object.keys(selectedFilterOptions).length) {\n      let newObj = {};\n      Object.keys(selectedFilterOptions).map(key => {\n        if (typeof selectedFilterOptions[key] === \"string\") {\n          newObj[key] = \"\";\n        } else {\n          newObj[key] = [];\n        }\n      });\n      setSelectedFilterOptions({\n        ...newObj\n      });\n      buildQueryParams({\n        ...newObj\n      });\n    }\n    setCurrentPage(1);\n  };\n\n  // Export the fetched data into an Excel file\n  const dispatch = useDispatch();\n  const exportToExcel = async () => {\n    try {\n      // Fetch all data items for Excel export\n      const result = await dispatch(teamApi.endpoints.getTeamData.initiate({\n        sort_by: sortColumn,\n        order: sortDirection,\n        page: currentPage,\n        per_page: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 10,\n        // Fallback value to avoid undefined issues\n        query: queryString\n      })).unwrap(); // Wait for the API response\n\n      if (!(result !== null && result !== void 0 && result.total) || result.total < 1) {\n        return false;\n      }\n      var sl = 1;\n      let prepXlsData = result.data.map(item => {\n        if (columns.length) {\n          let obj = {};\n          columns.forEach(column => {\n            if (!column.omit && column.selector) {\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\n            }\n          });\n          return obj;\n        }\n      });\n\n      // Create a worksheet from the JSON data and append to a new workbook\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\n      const workbook = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\n\n      // Convert workbook to a buffer and create a Blob to trigger a file download\n      const excelBuffer = XLSX.write(workbook, {\n        bookType: \"xlsx\",\n        type: \"array\"\n      });\n      const blob = new Blob([excelBuffer], {\n        type: \"application/octet-stream\"\n      });\n      saveAs(blob, `${MODULE_NAME.replace(/ /g, \"_\")}_${prepXlsData.length}.xlsx`);\n    } catch (error) {\n      console.error(\"Error exporting to Excel:\", error);\n    }\n  };\n\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\n  const fetchDataOptionsForFilterBy = useCallback(async (itemObject = {}, type = \"group\", searching = \"\", fieldType = \"select\") => {\n    let groupByField = itemObject.db_field || \"title\";\n    try {\n      setShowFilterOption(groupByField);\n      setFilterOptionLoading(true);\n      var groupData = [];\n      const response = await triggerFilterByFetch({\n        type: type.trim(),\n        column: groupByField.trim(),\n        text: searching.trim()\n      });\n      if (response.data) {\n        groupData = response.data;\n      }\n      if (groupData.length) {\n        if (fieldType === \"searchable\") {\n          setFilterOptions(prev => ({\n            ...prev,\n            [groupByField]: groupData\n          }));\n          return groupData;\n        }\n        const optionsForFilter = groupData.map(item => {\n          if (itemObject.selector) {\n            let label = itemObject.selector(item);\n            if (label) {\n              if (item.total && item.total > 1) {\n                label += ` (${item.total})`;\n              }\n              return {\n                label,\n                value: item[groupByField]\n              };\n            }\n            return null;\n          }\n        }).filter(Boolean);\n        setFilterOptions(prev => ({\n          ...prev,\n          [itemObject.id]: sortByLabel(optionsForFilter)\n        }));\n        return optionsForFilter;\n      }\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setFilterOptionLoading(false);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-auto pb-6 \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4/12 md:w-10/12 text-start\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold \",\n            children: MODULE_NAME\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8/12 flex items-end justify-end gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(ManageColumns, {\n            columns: columns,\n            setColumns: setColumns\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), !isFetching && dataItems && parseInt(dataItems.total) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",\n              onClick: exportToExcel,\n              children: [isFetching && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-outlined animate-spin text-sm me-2\",\n                  children: \"progress_activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false), !isFetching && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm me-2\",\n                children: \"file_export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this), \"Export to Excel (\", dataItems.total, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), rolePermissions.hasManagerRole && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",\n            onClick: () => setAddModalVisible(true),\n            children: \"Add New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchFilter, {\n        columns: columns,\n        selectedFilterOptions: selectedFilterOptions,\n        setSelectedFilterOptions: setSelectedFilterOptions,\n        fetchDataOptionsForFilterBy: fetchDataOptionsForFilterBy,\n        filterOptions: filterOptions,\n        filterOptionLoading: filterOptionLoading,\n        showFilterOption: showFilterOption,\n        resetPage: resetPage,\n        setCurrentPage: setCurrentPage,\n        buildQueryParams: buildQueryParams\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 9\n      }, this), fetchError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 24\n      }, this), isFetching && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 24\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 p-0 pb-1 rounded-lg my-5 \",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          columns: columns,\n          data: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [],\n          className: \"p-0 scrollbar-horizontal-10\",\n          fixedHeader: true,\n          highlightOnHover: true,\n          responsive: true,\n          pagination: true,\n          paginationServer: true,\n          paginationPerPage: perPage,\n          paginationTotalRows: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 0,\n          onChangePage: page => {\n            if (page !== currentPage) {\n              setCurrentPage(page);\n            }\n          },\n          onChangeRowsPerPage: newPerPage => {\n            if (newPerPage !== perPage) {\n              setPerPage(newPerPage);\n              setCurrentPage(1);\n            }\n          },\n          paginationComponentOptions: {\n            selectAllRowsItem: true,\n            selectAllRowsItemText: \"ALL\"\n          },\n          sortServer: true,\n          onSort: (column, sortDirection = \"desc\") => {\n            if (Object.keys(column).length) {\n              setSortColumn(column.db_field || column.name || \"created_at\");\n              setSortDirection(sortDirection || \"desc\");\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this), addModalVisible && /*#__PURE__*/_jsxDEV(AddTeam, {\n        isVisible: addModalVisible,\n        setVisible: setAddModalVisible\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 11\n      }, this), modalVisible && /*#__PURE__*/_jsxDEV(EditTeam, {\n        isVisible: modalVisible,\n        setVisible: setModalVisible,\n        dataItemsId: dataItemsId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 11\n      }, this), viewData &&\n      /*#__PURE__*/\n      // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\n      _jsxDEV(TableView, {\n        item: viewData,\n        setViewData: setViewData,\n        columns: columns,\n        handleEdit: handleEdit,\n        handleDelete: handleDelete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 616,\n    columnNumber: 5\n  }, this);\n};\n_s(TeamDataList, \"Xy2B/PMaKkGvvSbIc66Yo1j1J+0=\", false, function () {\n  return [useNavigate, useGetTeamDataQuery, useLazyFetchDataOptionsForTeamQuery, useDeleteTeamMutation, useRoleBasedAccess, useDispatch];\n});\n_c = TeamDataList;\nexport default TeamDataList;\nvar _c;\n$RefreshReg$(_c, \"TeamDataList\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "DataTable", "Loading", "<PERSON><PERSON><PERSON><PERSON>", "Image", "ManageColumns", "SearchFilter", "TableView", "useDispatch", "defaultDateTimeFormat", "defaultTimeFormat", "<PERSON><PERSON><PERSON><PERSON>", "sortByLabel", "saveAs", "XLSX", "teamApi", "useDeleteTeamMutation", "useGetTeamDataQuery", "useLazyFetchDataOptionsForTeamQuery", "useNavigate", "EditTeam", "AddTeam", "useRoleBasedAccess", "DateTimeFormatDay", "DateTimeFormatHour", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MODULE_NAME", "TeamDataList", "_s", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "dataItemsId", "setDataItemsId", "error", "setError", "viewData", "setViewData", "navigate", "addModalVisible", "setAddModalVisible", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "deleteTeam", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "value", "Array", "isArray", "vals", "map", "i", "join", "handleCopy", "keysToRemove", "cleanedData", "handleEdit", "id", "handleDelete", "onConfirm", "columnSerial", "rolePermissions", "columns", "setColumns", "name", "width", "className", "cell", "item", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "sortable", "filterable", "poc", "manager", "team_lead", "workday", "workdays", "JSON", "parse", "e", "billable_hours", "daily_billable_hours", "weekly_billable_hours", "launch", "logo", "src", "process", "env", "REACT_APP_BASE_STORAGE_URL", "alt", "icon", "_row$creator", "_row$creator2", "creator", "fname", "lname", "created_at", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "resetPage", "keys", "length", "newObj", "dispatch", "exportToExcel", "result", "endpoints", "getTeamData", "initiate", "total", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "column", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "write", "bookType", "type", "blob", "Blob", "replace", "console", "fetchDataOptionsForFilterBy", "itemObject", "searching", "fieldType", "groupByField", "response", "trim", "text", "prev", "optionsForFilter", "label", "filter", "Boolean", "message", "parseInt", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "isVisible", "setVisible", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team/TeamDataList.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {\r\n  confirmationAlert,\r\n  Image,\r\n  ManageColumns,\r\n  SearchFilter,\r\n  TableView,\r\n} from \"./../../common/coreui\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport {\r\n  defaultDateTimeFormat,\r\n  defaultTimeFormat,\r\n  removeKeys,\r\n  sortByLabel,\r\n} from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport {\r\n  teamApi,\r\n  useDeleteTeamMutation,\r\n  useGetTeamDataQuery,\r\n  useLazyFetchDataOptionsForTeamQuery,\r\n} from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditTeam from \"./EditTeam\";\r\nimport AddTeam from \"./AddTeam\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport {\r\n  DateTimeFormatDay,\r\n  DateTimeFormatHour,\r\n} from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Creative Team\";\r\n\r\n// Main component for listing Product Type List\r\nconst TeamDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  const {\r\n    data: dataItems,\r\n    isFetching,\r\n    error: fetchError,\r\n  } = useGetTeamDataQuery({\r\n    sort_by: sortColumn,\r\n    order: sortDirection,\r\n    page: currentPage,\r\n    per_page: perPage,\r\n    query: queryString,\r\n  });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] =\r\n    useLazyFetchDataOptionsForTeamQuery();\r\n\r\n  const [deleteTeam] = useDeleteTeamMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\");\r\n\r\n    setQueryString(q);\r\n  };\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\r\n      \"id\",\r\n      \"team\",\r\n      \"department\",\r\n      \"updated_at\",\r\n      \"updated_by\",\r\n      \"updater\",\r\n      \"created_at\",\r\n      \"creator\",\r\n      \"created_by\",\r\n      \"updated_by\",\r\n    ];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null);\r\n    setDataItemsId(id);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({\r\n      onConfirm: () => {\r\n        deleteTeam(id);\r\n        setViewData(null);\r\n      },\r\n    });\r\n  };\r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">\r\n              visibility\r\n            </span>\r\n          </button>\r\n\r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">\r\n                stylus_note\r\n              </span>\r\n            </button>\r\n          )}\r\n\r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">\r\n                content_copy\r\n              </span>\r\n            </button>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    // {\r\n    //     id: columnSerial++,\r\n    //   name: \"Department\",\r\n    //   selector: (row) => row.department?.name || \"N/A\",\r\n    //   db_title_field: \"department.name\",\r\n    //   db_field: \"department_id\",\r\n    //   sortable: true,\r\n    //   omit: false,\r\n    //   filterable: true,\r\n    // },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Name\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Point of Contact\",\r\n      db_field: \"poc\",\r\n      selector: (row) => row.poc || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Manager\",\r\n      db_field: \"manager\",\r\n      selector: (row) => row.manager || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Lead\",\r\n      db_field: \"team_lead\",\r\n      selector: (row) => row.team_lead || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Work Days\",\r\n      db_field: \"workday\",\r\n      selector: (row) => {\r\n        if (row.workday) {\r\n          try {\r\n            // If workday is a string, parse it as JSON\r\n            const workdays =\r\n              typeof row.workday === \"string\"\r\n                ? JSON.parse(row.workday)\r\n                : row.workday;\r\n            return Array.isArray(workdays) ? workdays.join(\", \") : row.workday;\r\n          } catch (e) {\r\n            // If parsing fails, return as is\r\n            return row.workday;\r\n          }\r\n        }\r\n        return \"\";\r\n      },\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    \r\n    {\r\n      id: columnSerial++,\r\n      name: \"Billable Hours\",\r\n      db_field: \"billable_hours\",\r\n      selector: (row) => row.billable_hours || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Daily Billable Hours\",\r\n      db_field: \"daily_billable_hours\",\r\n      selector: (row) => row.daily_billable_hours || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Weekly Billable Hours\",\r\n      db_field: \"weekly_billable_hours\",\r\n      selector: (row) => row.weekly_billable_hours || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Launch Date\",\r\n      db_field: \"launch\",\r\n      selector: (row) => row.launch || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Logo\",\r\n      width: \"150px\",\r\n      db_field: \"logo\",\r\n      selector: (row) => (row?.logo ? \"Has Logo\" : \"No Logo\"),\r\n      cell: (row) => (\r\n        <div className=\"flex align-middle items-center justify-center px-2 py-2\">\r\n          {row?.logo ? (\r\n            <Image\r\n              src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row?.logo}`}\r\n              alt=\"Team Logo\"\r\n              className=\"w-28 rounded-full object-cover\"\r\n            />\r\n          ) : (\r\n            <span className=\"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\">\r\n              No <br />\r\n              Logo\r\n            </span>\r\n          )}\r\n        </div>\r\n      ),\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Icon\",\r\n      width: \"120px\",\r\n      db_field: \"icon\",\r\n      selector: (row) => (row?.icon ? \"Has Icon\" : \"No Icon\"),\r\n      cell: (row) => (\r\n        <div className=\"flex align-middle items-center justify-center px-2 py-2\">\r\n          {row?.icon ? (\r\n            <Image\r\n              src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row?.icon}`}\r\n              alt=\"Team Icon\"\r\n              className=\"w-10 h-10 rounded-full object-cover\"\r\n            />\r\n          ) : (\r\n            <span className=\"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\">\r\n              No <br />\r\n              Icon\r\n            </span>\r\n          )}\r\n        </div>\r\n      ),\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created by\",\r\n      selector: (row) =>\r\n        `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n      db_field: \"created_by\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Time\",\r\n      selector: (row) => DateTimeFormatHour(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated by\",\r\n      selector: (row) =>\r\n        `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n      db_field: \"updated_by\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Time\",\r\n      selector: (row) => DateTimeFormatHour(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: false,\r\n    },\r\n  ]);\r\n\r\n  useEffect(() => {\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">\r\n                    visibility\r\n                  </span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">\r\n                      stylus_note\r\n                    </span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleCopy(item)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">\r\n                      content_copy\r\n                    </span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleDelete(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-sm\">\r\n                      delete\r\n                    </span>\r\n                  </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj });\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        teamApi.endpoints.getTeamData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n\r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n\r\n      var sl = 1;\r\n\r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] =\r\n                column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n\r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n\r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], {\r\n        type: \"application/octet-stream\",\r\n      });\r\n      saveAs(\r\n        blob,\r\n        `${MODULE_NAME.replace(/ /g, \"_\")}_${prepXlsData.length}.xlsx`\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({\r\n          type: type.trim(),\r\n          column: groupByField.trim(),\r\n          text: searching.trim(),\r\n        });\r\n\r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if (itemObject.selector) {\r\n                let label = itemObject.selector(item);\r\n\r\n                if (label) {\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n                return null;\r\n              }\r\n            })\r\n            .filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n\r\n            {/* Export to Excel button, only shown if data exists */}\r\n            {!isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                      file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n          columns={columns}\r\n          selectedFilterOptions={selectedFilterOptions}\r\n          setSelectedFilterOptions={setSelectedFilterOptions}\r\n          fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n          filterOptions={filterOptions}\r\n          filterOptionLoading={filterOptionLoading}\r\n          showFilterOption={showFilterOption}\r\n          resetPage={resetPage}\r\n          setCurrentPage={setCurrentPage}\r\n          buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n\r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if (newPerPage !== perPage) {\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection = \"desc\") => {\r\n              if (Object.keys(column).length) {\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n          <AddTeam\r\n            isVisible={addModalVisible}\r\n            setVisible={setAddModalVisible}\r\n          />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditTeam\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView\r\n            item={viewData}\r\n            setViewData={setViewData}\r\n            columns={columns}\r\n            handleEdit={handleEdit}\r\n            handleDelete={handleDelete}\r\n          />\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TeamDataList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,OAAO,MAAM,wBAAwB;AAE5C,SACEC,iBAAiB,EACjBC,KAAK,EACLC,aAAa,EACbC,YAAY,EACZC,SAAS,QACJ,uBAAuB;AAE9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,qBAAqB,EACrBC,iBAAiB,EACjBC,UAAU,EACVC,WAAW,QACN,eAAe;;AAEtB;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SACEC,OAAO,EACPC,qBAAqB,EACrBC,mBAAmB,EACnBC,mCAAmC,QAC9B,sBAAsB;AAC7B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SACEC,iBAAiB,EACjBC,kBAAkB,QACb,kCAAkC;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAG,eAAe;;AAEnC;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACoC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMoD,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,YAAY,CAAC;EAC1D,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM;IACJ+D,IAAI,EAAEC,SAAS;IACfC,UAAU;IACVjB,KAAK,EAAEkB;EACT,CAAC,GAAG/C,mBAAmB,CAAC;IACtBgD,OAAO,EAAEZ,UAAU;IACnBa,KAAK,EAAEX,aAAa;IACpBY,IAAI,EAAER,WAAW;IACjBS,QAAQ,EAAEX,OAAO;IACjBY,KAAK,EAAE/B;EACT,CAAC,CAAC;EAEF,MAAM,CAACgC,oBAAoB,EAAE;IAAET,IAAI,EAAEU,SAAS;IAAEzB,KAAK,EAAE0B;EAAe,CAAC,CAAC,GACtEtD,mCAAmC,CAAC,CAAC;EAEvC,MAAM,CAACuD,UAAU,CAAC,GAAGzD,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAM0D,gBAAgB,GAAIC,eAAe,IAAK;IAC5C,IAAIC,CAAC,GAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOF,GAAG,GAAG,IAAIC,GAAG,IAAIC,KAAK,EAAE;MACjC;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACxB,MAAMG,IAAI,GAAGH,KAAK,CAACI,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACL,KAAK,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;QAChD,OAAOR,GAAG,GAAG,IAAIC,GAAG,IAAII,IAAI,EAAE;MAChC;MACA,OAAOL,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAENzC,cAAc,CAACqC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMa,UAAU,GAAI5B,IAAI,IAAK;IAC3B,MAAM6B,YAAY,GAAG,CACnB,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,YAAY,CACb;IACD,MAAMC,WAAW,GAAGhF,UAAU,CAACkD,IAAI,EAAE6B,YAAY,CAAC;IAClDzC,WAAW,CAAC,IAAI,CAAC;IACjBR,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmD,UAAU,GAAIC,EAAE,IAAK;IACzB5C,WAAW,CAAC,IAAI,CAAC;IACjBJ,cAAc,CAACgD,EAAE,CAAC;IAClBpD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqD,YAAY,GAAID,EAAE,IAAK;IAC3B1F,iBAAiB,CAAC;MAChB4F,SAAS,EAAEA,CAAA,KAAM;QACftB,UAAU,CAACoB,EAAE,CAAC;QACd5C,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI+C,YAAY,GAAG,CAAC;EAEpB,MAAM;IAAEC;EAAgB,CAAC,GAAG3E,kBAAkB,CAAC,CAAC;;EAEhD;EACA,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAGrG,QAAQ,CAAC,MAAM,CAC3C;IACE+F,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,YAAY;IACvBC,IAAI,EAAGC,IAAI,iBACT9E,OAAA;MAAK4E,SAAS,EAAC,qCAAqC;MAAAG,QAAA,gBAElD/E,OAAA;QACE4E,SAAS,EAAC,uLAAuL;QACjMI,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAACuD,IAAI,CAAE;QAAAC,QAAA,eAEjC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAEpD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGR,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,mLAAmL;QAC7LI,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE;QAAAY,QAAA,eAEnC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAEpD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,EAGA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,qLAAqL;QAC/LI,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACe,IAAI,CAAE;QAAAC,QAAA,eAEhC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAEpD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,EAGA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,mLAAmL;QAC7LI,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE;QAAAY,QAAA,eAErC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEjB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,MAAM;IACZY,QAAQ,EAAEA,CAACC,GAAG,EAAEC,KAAK,KAAK,CAACvD,WAAW,GAAG,CAAC,IAAIF,OAAO,GAAGyD,KAAK,GAAG,CAAC;IACjEb,KAAK,EAAE,MAAM;IACbc,IAAI,EAAE;EACR,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBgB,QAAQ,EAAE,MAAM;IAChBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACb,IAAI,IAAI,EAAE;IACjCe,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,kBAAkB;IACxBgB,QAAQ,EAAE,KAAK;IACfJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACM,GAAG,IAAI,EAAE;IAChCJ,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,SAAS;IACfgB,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACO,OAAO,IAAI,EAAE;IACpCL,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBgB,QAAQ,EAAE,WAAW;IACrBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACQ,SAAS,IAAI,EAAE;IACtCN,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBgB,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAGC,GAAG,IAAK;MACjB,IAAIA,GAAG,CAACS,OAAO,EAAE;QACf,IAAI;UACF;UACA,MAAMC,QAAQ,GACZ,OAAOV,GAAG,CAACS,OAAO,KAAK,QAAQ,GAC3BE,IAAI,CAACC,KAAK,CAACZ,GAAG,CAACS,OAAO,CAAC,GACvBT,GAAG,CAACS,OAAO;UACjB,OAAOvC,KAAK,CAACC,OAAO,CAACuC,QAAQ,CAAC,GAAGA,QAAQ,CAACnC,IAAI,CAAC,IAAI,CAAC,GAAGyB,GAAG,CAACS,OAAO;QACpE,CAAC,CAAC,OAAOI,CAAC,EAAE;UACV;UACA,OAAOb,GAAG,CAACS,OAAO;QACpB;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACDP,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EAED;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,gBAAgB;IACtBgB,QAAQ,EAAE,gBAAgB;IAC1BJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACc,cAAc,IAAI,EAAE;IAC3CZ,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,sBAAsB;IAC5BgB,QAAQ,EAAE,sBAAsB;IAChCJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACe,oBAAoB,IAAI,EAAE;IACjDb,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,uBAAuB;IAC7BgB,QAAQ,EAAE,uBAAuB;IACjCJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACgB,qBAAqB,IAAI,EAAE;IAClDd,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,aAAa;IACnBgB,QAAQ,EAAE,QAAQ;IAClBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACiB,MAAM,IAAI,EAAE;IACnCf,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,OAAO;IACde,QAAQ,EAAE,MAAM;IAChBJ,QAAQ,EAAGC,GAAG,IAAMA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEkB,IAAI,GAAG,UAAU,GAAG,SAAU;IACvD5B,IAAI,EAAGU,GAAG,iBACRvF,OAAA;MAAK4E,SAAS,EAAC,yDAAyD;MAAAG,QAAA,EACrEQ,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEkB,IAAI,gBACRzG,OAAA,CAACtB,KAAK;QACJgI,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAItB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEkB,IAAI,EAAG;QAC9DK,GAAG,EAAC,WAAW;QACflC,SAAS,EAAC;MAAgC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,gBAEFpF,OAAA;QAAM4E,SAAS,EAAC,yHAAyH;QAAAG,QAAA,GAAC,KACrI,eAAA/E,OAAA;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,QAEX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDO,QAAQ,EAAE,IAAI;IACdF,IAAI,EAAE,KAAK;IACXG,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,OAAO;IACde,QAAQ,EAAE,MAAM;IAChBJ,QAAQ,EAAGC,GAAG,IAAMA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEwB,IAAI,GAAG,UAAU,GAAG,SAAU;IACvDlC,IAAI,EAAGU,GAAG,iBACRvF,OAAA;MAAK4E,SAAS,EAAC,yDAAyD;MAAAG,QAAA,EACrEQ,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEwB,IAAI,gBACR/G,OAAA,CAACtB,KAAK;QACJgI,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAItB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEwB,IAAI,EAAG;QAC9DD,GAAG,EAAC,WAAW;QACflC,SAAS,EAAC;MAAqC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,gBAEFpF,OAAA;QAAM4E,SAAS,EAAC,yHAAyH;QAAAG,QAAA,GAAC,KACrI,eAAA/E,OAAA;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,QAEX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDO,QAAQ,EAAE,IAAI;IACdF,IAAI,EAAE,KAAK;IACXG,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBY,QAAQ,EAAGC,GAAG;MAAA,IAAAyB,YAAA,EAAAC,aAAA;MAAA,OACZ,GAAG,EAAAD,YAAA,GAAAzB,GAAG,CAAC2B,OAAO,cAAAF,YAAA,uBAAXA,YAAA,CAAaG,KAAK,KAAI,EAAE,IAAI,EAAAF,aAAA,GAAA1B,GAAG,CAAC2B,OAAO,cAAAD,aAAA,uBAAXA,aAAA,CAAaG,KAAK,KAAI,EAAE,EAAE;IAAA;IAC3D1B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,cAAc;IACpBY,QAAQ,EAAGC,GAAG,IAAK1F,iBAAiB,CAAC0F,GAAG,CAAC8B,UAAU,CAAC;IACpD3B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,cAAc;IACpBY,QAAQ,EAAGC,GAAG,IAAKzF,kBAAkB,CAACyF,GAAG,CAAC8B,UAAU,CAAC;IACrD3B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBY,QAAQ,EAAGC,GAAG;MAAA,IAAA+B,YAAA,EAAAC,aAAA;MAAA,OACZ,GAAG,EAAAD,YAAA,GAAA/B,GAAG,CAACiC,OAAO,cAAAF,YAAA,uBAAXA,YAAA,CAAaH,KAAK,KAAI,EAAE,IAAI,EAAAI,aAAA,GAAAhC,GAAG,CAACiC,OAAO,cAAAD,aAAA,uBAAXA,aAAA,CAAaH,KAAK,KAAI,EAAE,EAAE;IAAA;IAC3D1B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,cAAc;IACpBY,QAAQ,EAAGC,GAAG,IAAK1F,iBAAiB,CAAC0F,GAAG,CAACkC,UAAU,CAAC;IACpD/B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEzB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,cAAc;IACpBY,QAAQ,EAAGC,GAAG,IAAKzF,kBAAkB,CAACyF,GAAG,CAACkC,UAAU,CAAC;IACrD/B,QAAQ,EAAE,YAAY;IACtBD,IAAI,EAAE,KAAK;IACXE,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;EAEFtH,SAAS,CAAC,MAAM;IACdmG,UAAU,CAAEiD,WAAW,IAAK,CAC1B,GAAGA,WAAW,CAAC9D,GAAG,CAAE+D,GAAG,IAAK;MAC1B,IAAIA,GAAG,CAACjD,IAAI,KAAK,QAAQ,EAAE;QACzB;QACA,OAAO;UACL,GAAGiD,GAAG;UACN9C,IAAI,EAAGC,IAAI,iBACT9E,OAAA;YAAK4E,SAAS,EAAC,qCAAqC;YAAAG,QAAA,gBAClD/E,OAAA;cACE4E,SAAS,EAAC,uLAAuL;cACjMI,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAACuD,IAAI,CAAE;cAAAC,QAAA,eAEjC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACR,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;cACE4E,SAAS,EAAC,mLAAmL;cAC7LI,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE;cAAAY,QAAA,eAEnC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT,EAEA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;cACE4E,SAAS,EAAC,qLAAqL;cAC/LI,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACe,IAAI,CAAE;cAAAC,QAAA,eAEhC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT,EAEA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;cACE4E,SAAS,EAAC,mLAAmL;cAC7LI,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE;cAAAY,QAAA,eAErC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC;MACH;MACA,OAAOuC,GAAG;IACZ,CAAC,CAAC,CACH,CAAC;EACJ,CAAC,EAAE,CAACpD,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEvB;EACA,MAAMqD,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIzE,MAAM,CAAC0E,IAAI,CAACrH,qBAAqB,CAAC,CAACsH,MAAM,EAAE;MAC7C,IAAIC,MAAM,GAAG,CAAC,CAAC;MACf5E,MAAM,CAAC0E,IAAI,CAACrH,qBAAqB,CAAC,CAACoD,GAAG,CAAEL,GAAG,IAAK;QAC9C,IAAI,OAAO/C,qBAAqB,CAAC+C,GAAG,CAAC,KAAK,QAAQ,EAAE;UAClDwE,MAAM,CAACxE,GAAG,CAAC,GAAG,EAAE;QAClB,CAAC,MAAM;UACLwE,MAAM,CAACxE,GAAG,CAAC,GAAG,EAAE;QAClB;MACF,CAAC,CAAC;MACF9C,wBAAwB,CAAC;QAAE,GAAGsH;MAAO,CAAC,CAAC;MACvC/E,gBAAgB,CAAC;QAAE,GAAG+E;MAAO,CAAC,CAAC;IACjC;IACA7F,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAM8F,QAAQ,GAAGlJ,WAAW,CAAC,CAAC;EAC9B,MAAMmJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,MAAM,GAAG,MAAMF,QAAQ,CAC3B3I,OAAO,CAAC8I,SAAS,CAACC,WAAW,CAACC,QAAQ,CAAC;QACrC9F,OAAO,EAAEZ,UAAU;QACnBa,KAAK,EAAEX,aAAa;QACpBY,IAAI,EAAER,WAAW;QACjBS,QAAQ,EAAE,CAAAN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkG,KAAK,KAAI,EAAE;QAAE;QAClC3F,KAAK,EAAE/B;MACT,CAAC,CACH,CAAC,CAAC2H,MAAM,CAAC,CAAC,CAAC,CAAC;;MAEZ,IAAI,EAACL,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEI,KAAK,KAAIJ,MAAM,CAACI,KAAK,GAAG,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MAEA,IAAIE,EAAE,GAAG,CAAC;MAEV,IAAIC,WAAW,GAAGP,MAAM,CAAC/F,IAAI,CAACyB,GAAG,CAAEkB,IAAI,IAAK;QAC1C,IAAIN,OAAO,CAACsD,MAAM,EAAE;UAClB,IAAIY,GAAG,GAAG,CAAC,CAAC;UACZlE,OAAO,CAACmE,OAAO,CAAEC,MAAM,IAAK;YAC1B,IAAI,CAACA,MAAM,CAACnD,IAAI,IAAImD,MAAM,CAACtD,QAAQ,EAAE;cACnCoD,GAAG,CAACE,MAAM,CAAClE,IAAI,CAAC,GACdkE,MAAM,CAAClE,IAAI,KAAK,MAAM,GAAG8D,EAAE,EAAE,GAAGI,MAAM,CAACtD,QAAQ,CAACR,IAAI,CAAC,IAAI,EAAE;YAC/D;UACF,CAAC,CAAC;UACF,OAAO4D,GAAG;QACZ;MACF,CAAC,CAAC;;MAEF;MACA,MAAMG,SAAS,GAAGzJ,IAAI,CAAC0J,KAAK,CAACC,aAAa,CAACN,WAAW,CAAC;MACvD,MAAMO,QAAQ,GAAG5J,IAAI,CAAC0J,KAAK,CAACG,QAAQ,CAAC,CAAC;MACtC7J,IAAI,CAAC0J,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,QAAQ,CAAC;;MAE3D;MACA,MAAMM,WAAW,GAAG/J,IAAI,CAACgK,KAAK,CAACJ,QAAQ,EAAE;QACvCK,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,WAAW,CAAC,EAAE;QACnCG,IAAI,EAAE;MACR,CAAC,CAAC;MACFnK,MAAM,CACJoK,IAAI,EACJ,GAAGpJ,WAAW,CAACsJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAIhB,WAAW,CAACX,MAAM,OACzD,CAAC;IACH,CAAC,CAAC,OAAO1G,KAAK,EAAE;MACdsI,OAAO,CAACtI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMuI,2BAA2B,GAAGtL,WAAW,CAC7C,OACEuL,UAAU,GAAG,CAAC,CAAC,EACfN,IAAI,GAAG,OAAO,EACdO,SAAS,GAAG,EAAE,EACdC,SAAS,GAAG,QAAQ,KACjB;IACH,IAAIC,YAAY,GAAGH,UAAU,CAAClE,QAAQ,IAAI,OAAO;IAEjD,IAAI;MACF/E,mBAAmB,CAACoJ,YAAY,CAAC;MACjC9I,sBAAsB,CAAC,IAAI,CAAC;MAE5B,IAAI4B,SAAS,GAAG,EAAE;MAElB,MAAMmH,QAAQ,GAAG,MAAMpH,oBAAoB,CAAC;QAC1C0G,IAAI,EAAEA,IAAI,CAACW,IAAI,CAAC,CAAC;QACjBrB,MAAM,EAAEmB,YAAY,CAACE,IAAI,CAAC,CAAC;QAC3BC,IAAI,EAAEL,SAAS,CAACI,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAID,QAAQ,CAAC7H,IAAI,EAAE;QACjBU,SAAS,GAAGmH,QAAQ,CAAC7H,IAAI;MAC3B;MAEA,IAAIU,SAAS,CAACiF,MAAM,EAAE;QACpB,IAAIgC,SAAS,KAAK,YAAY,EAAE;UAC9BvJ,gBAAgB,CAAE4J,IAAI,KAAM;YAC1B,GAAGA,IAAI;YACP,CAACJ,YAAY,GAAGlH;UAClB,CAAC,CAAC,CAAC;UAEH,OAAOA,SAAS;QAClB;QAEA,MAAMuH,gBAAgB,GAAGvH,SAAS,CAC/Be,GAAG,CAAEkB,IAAI,IAAK;UACb,IAAI8E,UAAU,CAACtE,QAAQ,EAAE;YACvB,IAAI+E,KAAK,GAAGT,UAAU,CAACtE,QAAQ,CAACR,IAAI,CAAC;YAErC,IAAIuF,KAAK,EAAE;cACT,IAAIvF,IAAI,CAACwD,KAAK,IAAIxD,IAAI,CAACwD,KAAK,GAAG,CAAC,EAAE;gBAChC+B,KAAK,IAAI,KAAKvF,IAAI,CAACwD,KAAK,GAAG;cAC7B;cAEA,OAAO;gBAAE+B,KAAK;gBAAE7G,KAAK,EAAEsB,IAAI,CAACiF,YAAY;cAAE,CAAC;YAC7C;YAEA,OAAO,IAAI;UACb;QACF,CAAC,CAAC,CACDO,MAAM,CAACC,OAAO,CAAC;QAElBhK,gBAAgB,CAAE4J,IAAI,KAAM;UAC1B,GAAGA,IAAI;UACP,CAACP,UAAU,CAACzF,EAAE,GAAGjF,WAAW,CAACkL,gBAAgB;QAC/C,CAAC,CAAC,CAAC;QAEH,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC,OAAOhJ,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACoJ,OAAO,CAAC;IACzB,CAAC,SAAS;MACRvJ,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC,EACD,EACF,CAAC;EAED,oBACEjB,OAAA;IAAS4E,SAAS,EAAC,+DAA+D;IAAAG,QAAA,eAChF/E,OAAA;MAAK4E,SAAS,EAAC,eAAe;MAAAG,QAAA,gBAE5B/E,OAAA;QAAK4E,SAAS,EAAC,gGAAgG;QAAAG,QAAA,gBAC7G/E,OAAA;UAAK4E,SAAS,EAAC,8BAA8B;UAAAG,QAAA,eAC3C/E,OAAA;YAAI4E,SAAS,EAAC,qBAAqB;YAAAG,QAAA,EAAE5E;UAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNpF,OAAA;UAAK4E,SAAS,EAAC,yCAAyC;UAAAG,QAAA,gBAEtD/E,OAAA,CAACrB,aAAa;YAAC6F,OAAO,EAAEA,OAAQ;YAACC,UAAU,EAAEA;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAG1D,CAAC/C,UAAU,IAAID,SAAS,IAAIqI,QAAQ,CAACrI,SAAS,CAACkG,KAAK,CAAC,GAAG,CAAC,iBACxDtI,OAAA,CAAAE,SAAA;YAAA6E,QAAA,eACE/E,OAAA;cACE4E,SAAS,EAAC,mZAAmZ;cAC7ZI,OAAO,EAAEiD,aAAc;cAAAlD,QAAA,GAEtB1C,UAAU,iBACTrC,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,eACE/E,OAAA;kBAAM4E,SAAS,EAAC,qDAAqD;kBAAAG,QAAA,EAAC;gBAEtE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC,gBACP,CACH,EACA,CAAC/C,UAAU,iBACVrC,OAAA;gBAAM4E,SAAS,EAAC,wCAAwC;gBAAAG,QAAA,EAAC;cAEzD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EAAC,mBACe,EAAChD,SAAS,CAACkG,KAAK,EAAC,GACpC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC,gBACT,CACH,EAEAb,eAAe,CAACc,cAAc,iBAC7BrF,OAAA;YACE4E,SAAS,EAAC,+XAA+X;YACzYI,OAAO,EAAEA,CAAA,KAAMtD,kBAAkB,CAAC,IAAI,CAAE;YAAAqD,QAAA,EACzC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA,CAACpB,YAAY;QACX4F,OAAO,EAAEA,OAAQ;QACjBhE,qBAAqB,EAAEA,qBAAsB;QAC7CC,wBAAwB,EAAEA,wBAAyB;QACnDkJ,2BAA2B,EAAEA,2BAA4B;QACzDrJ,aAAa,EAAEA,aAAc;QAC7BU,mBAAmB,EAAEA,mBAAoB;QACzCN,gBAAgB,EAAEA,gBAAiB;QACnCkH,SAAS,EAAEA,SAAU;QACrB1F,cAAc,EAAEA,cAAe;QAC/Bc,gBAAgB,EAAEA;MAAiB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAGD9C,UAAU,iBAAItC,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAG,QAAA,EAAE3D;MAAK;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAEzD/C,UAAU,iBAAIrC,OAAA,CAACxB,OAAO;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAK1BpF,OAAA;QAAK4E,SAAS,EAAC,kDAAkD;QAAAG,QAAA,eAC/D/E,OAAA,CAACzB,SAAS;UACRiG,OAAO,EAAEA,OAAQ;UACjBrC,IAAI,EAAE,CAAAC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAG;UAC5ByC,SAAS,EAAC,6BAA6B;UACvC8F,WAAW;UACXC,gBAAgB;UAChBC,UAAU;UACVC,UAAU;UACVC,gBAAgB;UAChBC,iBAAiB,EAAEhJ,OAAQ;UAC3BiJ,mBAAmB,EAAE,CAAA5I,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkG,KAAK,KAAI,CAAE;UAC3C2C,YAAY,EAAGxI,IAAI,IAAK;YACtB,IAAIA,IAAI,KAAKR,WAAW,EAAE;cACxBC,cAAc,CAACO,IAAI,CAAC;YACtB;UACF,CAAE;UACFyI,mBAAmB,EAAGC,UAAU,IAAK;YACnC,IAAIA,UAAU,KAAKpJ,OAAO,EAAE;cAC1BC,UAAU,CAACmJ,UAAU,CAAC;cACtBjJ,cAAc,CAAC,CAAC,CAAC;YACnB;UACF,CAAE;UACFkJ,0BAA0B,EAAE;YAC1BC,iBAAiB,EAAE,IAAI;YACvBC,qBAAqB,EAAE;UACzB,CAAE;UACFC,UAAU;UACVC,MAAM,EAAEA,CAAC5C,MAAM,EAAE/G,aAAa,GAAG,MAAM,KAAK;YAC1C,IAAIsB,MAAM,CAAC0E,IAAI,CAACe,MAAM,CAAC,CAACd,MAAM,EAAE;cAC9BlG,aAAa,CAACgH,MAAM,CAAClD,QAAQ,IAAIkD,MAAM,CAAClE,IAAI,IAAI,YAAY,CAAC;cAC7D5C,gBAAgB,CAACD,aAAa,IAAI,MAAM,CAAC;YAC3C;UACF;QAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL3D,eAAe,iBACdzB,OAAA,CAACL,OAAO;QACN8L,SAAS,EAAEhK,eAAgB;QAC3BiK,UAAU,EAAEhK;MAAmB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF,EAGAtE,YAAY,iBACXd,OAAA,CAACN,QAAQ;QACP+L,SAAS,EAAE3K,YAAa;QACxB4K,UAAU,EAAE3K,eAAgB;QAC5BG,WAAW,EAAEA;MAAY;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACF,EAEA9D,QAAQ;MAAA;MACP;MACAtB,OAAA,CAACnB,SAAS;QACRiG,IAAI,EAAExD,QAAS;QACfC,WAAW,EAAEA,WAAY;QACzBiD,OAAO,EAAEA,OAAQ;QACjBN,UAAU,EAAEA,UAAW;QACvBE,YAAY,EAAEA;MAAa;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC/E,EAAA,CApsBID,YAAY;EAAA,QAWCX,WAAW,EAaxBF,mBAAmB,EASrBC,mCAAmC,EAEhBF,qBAAqB,EAqDdM,kBAAkB,EAqW7Bd,WAAW;AAAA;AAAA6M,EAAA,GA7bxBvL,YAAY;AAssBlB,eAAeA,YAAY;AAAC,IAAAuL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}