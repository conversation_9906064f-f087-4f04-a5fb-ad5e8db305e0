{"__meta": {"id": "Xdcc3322a2afbe1aba2a59e480372995e", "datetime": "2025-08-18 13:49:05", "utime": **********.834993, "method": "GET", "uri": "/api/schedule-planners?sort_by=&order=asc&page=1&per_page=10", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[13:49:05] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.789861, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.349505, "end": **********.83501, "duration": 0.4855051040649414, "duration_str": "486ms", "measures": [{"label": "Booting", "start": **********.349505, "relative_start": 0, "end": **********.750152, "relative_end": **********.750152, "duration": 0.4006471633911133, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.750159, "relative_start": 0.4006540775299072, "end": **********.835012, "relative_end": 1.9073486328125e-06, "duration": 0.08485293388366699, "duration_str": "84.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23800616, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/schedule-planners", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "as": "schedule-planners.index", "controller": "App\\Http\\Controllers\\SchedulePlannerController@index", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=12\" onclick=\"\">app/Http/Controllers/SchedulePlannerController.php:12-77</a>"}, "queries": {"nb_statements": 23, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00959, "accumulated_duration_str": "9.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.770286, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '444' limit 1", "type": "query", "params": [], "bindings": ["444"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.774956, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 19.708}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.781175, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 19.708, "width_percent": 3.545}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.787474, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 23.253, "width_percent": 6.257}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.790165, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 29.51, "width_percent": 5.318}, {"sql": "select count(*) as aggregate from `schedule_planner`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.792351, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 34.828, "width_percent": 3.754}, {"sql": "select * from `schedule_planner` order by `weeknum` asc, `department_id` asc, `team_id` asc, `schedule_id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.793911, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 38.582, "width_percent": 2.92}, {"sql": "select * from `users` where `users`.`id` in (67)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7954051, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 41.502, "width_percent": 3.128}, {"sql": "select * from `users` where `users`.`id` in (67)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7967799, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 44.63, "width_percent": 2.711}, {"sql": "select * from `teams` where `teams`.`id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7984178, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 47.341, "width_percent": 2.503}, {"sql": "select * from `departments` where `departments`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.79996, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 49.844, "width_percent": 2.086}, {"sql": "select * from `schedules` where `schedules`.`id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.801619, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:60", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=60", "ajax": false, "filename": "SchedulePlannerController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 51.929, "width_percent": 3.233}, {"sql": "select * from `users` where `id` in ('143', '144')", "type": "query", "params": [], "bindings": ["143", "144"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8039439, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 55.162, "width_percent": 6.361}, {"sql": "select `resource_types`.`name`, `resource_type_user`.`user_id` as `pivot_user_id`, `resource_type_user`.`resource_type_id` as `pivot_resource_type_id` from `resource_types` inner join `resource_type_user` on `resource_types`.`id` = `resource_type_user`.`resource_type_id` where `resource_type_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.806073, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 61.522, "width_percent": 4.067}, {"sql": "select `designations`.`name`, `designation_user`.`user_id` as `pivot_user_id`, `designation_user`.`designation_id` as `pivot_designation_id` from `designations` inner join `designation_user` on `designations`.`id` = `designation_user`.`designation_id` where `designation_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.808218, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 65.589, "width_percent": 3.233}, {"sql": "select * from `users` where `id` in ('143', '144')", "type": "query", "params": [], "bindings": ["143", "144"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.809591, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 68.822, "width_percent": 3.441}, {"sql": "select `resource_types`.`name`, `resource_type_user`.`user_id` as `pivot_user_id`, `resource_type_user`.`resource_type_id` as `pivot_resource_type_id` from `resource_types` inner join `resource_type_user` on `resource_types`.`id` = `resource_type_user`.`resource_type_id` where `resource_type_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8111048, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 72.263, "width_percent": 2.711}, {"sql": "select `designations`.`name`, `designation_user`.`user_id` as `pivot_user_id`, `designation_user`.`designation_id` as `pivot_designation_id` from `designations` inner join `designation_user` on `designations`.`id` = `designation_user`.`designation_id` where `designation_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.812452, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 74.974, "width_percent": 3.024}, {"sql": "select * from `users` where `id` in ('143', '144')", "type": "query", "params": [], "bindings": ["143", "144"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.813747, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 77.998, "width_percent": 3.65}, {"sql": "select `resource_types`.`name`, `resource_type_user`.`user_id` as `pivot_user_id`, `resource_type_user`.`resource_type_id` as `pivot_resource_type_id` from `resource_types` inner join `resource_type_user` on `resource_types`.`id` = `resource_type_user`.`resource_type_id` where `resource_type_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8152218, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 81.648, "width_percent": 2.607}, {"sql": "select `designations`.`name`, `designation_user`.`user_id` as `pivot_user_id`, `designation_user`.`designation_id` as `pivot_designation_id` from `designations` inner join `designation_user` on `designations`.`id` = `designation_user`.`designation_id` where `designation_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.81652, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 84.254, "width_percent": 2.607}, {"sql": "select * from `users` where `id` in ('143', '144')", "type": "query", "params": [], "bindings": ["143", "144"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.817885, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 86.861, "width_percent": 3.545}, {"sql": "select `resource_types`.`name`, `resource_type_user`.`user_id` as `pivot_user_id`, `resource_type_user`.`resource_type_id` as `pivot_resource_type_id` from `resource_types` inner join `resource_type_user` on `resource_types`.`id` = `resource_type_user`.`resource_type_id` where `resource_type_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.819637, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 90.407, "width_percent": 5.527}, {"sql": "select `designations`.`name`, `designation_user`.`user_id` as `pivot_user_id`, `designation_user`.`designation_id` as `pivot_designation_id` from `designations` inner join `designation_user` on `designations`.`id` = `designation_user`.`designation_id` where `designation_user`.`user_id` in (143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.821419, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "SchedulePlannerController.php:70", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SchedulePlannerController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\SchedulePlannerController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FSchedulePlannerController.php&line=70", "ajax": false, "filename": "SchedulePlannerController.php", "line": "70"}, "connection": "creative_app3", "explain": null, "start_percent": 95.933, "width_percent": 4.067}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Resource_type": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FResource_type.php&line=1", "ajax": false, "filename": "Resource_type.php", "line": "?"}}, "App\\Models\\Designation": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FDesignation.php&line=1", "ajax": false, "filename": "Designation.php", "line": "?"}}, "App\\Models\\SchedulePlanner": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FSchedulePlanner.php&line=1", "ajax": false, "filename": "SchedulePlanner.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}}, "count": 32, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/schedule-planners", "status_code": "<pre class=sf-dump id=sf-dump-1366563218 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1366563218\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1081752352 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort_by</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081752352\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1438934979 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1438934979\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1635971798 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 444|e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635971798\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-497842544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-497842544\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1313480452 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:49:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">148</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313480452\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1981402638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981402638\", {\"maxDepth\":0})</script>\n"}}