{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\AddTeam.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { alertMessage } from '../../common/coreui';\nimport Select from 'react-select';\nimport { X, Users, Building, User, Calendar, Clock, Upload, AlertCircle, CheckCircle } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst isTokenValid = () => {\n  const token = localStorage.getItem('token');\n  return token !== null;\n};\nconst AddTeam = ({\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [teamName, setTeamName] = useState('');\n  const [icon, setIcon] = useState(null);\n  const [logo, setLogo] = useState(null);\n  const [poc, setPoc] = useState('');\n  const [manager, setManager] = useState('');\n  const [teamLead, setTeamLead] = useState('');\n  const [launch, setLaunch] = useState('');\n  const [workday, setWorkday] = useState([]);\n  const [billableHours, setBillableHours] = useState('');\n  const [departmentId, setDepartmentId] = useState('');\n  const [error, setError] = useState('');\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // React Select states\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\n  const [selectedPoc, setSelectedPoc] = useState(null);\n  const [selectedManager, setSelectedManager] = useState(null);\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\n\n  // Days of the week for multi-select\n  const daysOfWeek = [{\n    value: 'Monday',\n    label: 'Monday'\n  }, {\n    value: 'Tuesday',\n    label: 'Tuesday'\n  }, {\n    value: 'Wednesday',\n    label: 'Wednesday'\n  }, {\n    value: 'Thursday',\n    label: 'Thursday'\n  }, {\n    value: 'Friday',\n    label: 'Friday'\n  }, {\n    value: 'Saturday',\n    label: 'Saturday'\n  }, {\n    value: 'Sunday',\n    label: 'Sunday'\n  }];\n\n  // Handle workday selection\n  const handleWorkdayChange = selectedOptions => {\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\n    setWorkday(selectedValues);\n  };\n\n  // Create options for React Select dropdowns\n  const departmentOptions = departments.map(dept => ({\n    value: dept.id,\n    label: dept.name\n  }));\n\n  // Filter users based on responsibility levels\n  const getUserOptions = allowedRoles => {\n    return users.filter(user => {\n      var _user$resource_types;\n      // Check if user has any of the allowed roles\n      const hasValidResponsibility = (_user$resource_types = user.resource_types) === null || _user$resource_types === void 0 ? void 0 : _user$resource_types.some(rt => allowedRoles.includes(rt));\n      // Ensure user has a valid name\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\n      return hasValidResponsibility && hasValidName;\n    }).map(user => ({\n      value: user.fullName,\n      label: user.fullName\n    }));\n  };\n\n  // Updated filtering based on responsibility level\n  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\n  const managerOptions = getUserOptions(['Manager', 'HOD']);\n  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!isVisible || !isTokenValid()) {\n        return;\n      }\n      const token = localStorage.getItem('token');\n      try {\n        setLoading(true);\n        setError('');\n\n        // Fetch Users\n        const usersResponse = await fetch(`${API_URL}/users`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!usersResponse.ok) {\n          throw new Error('Failed to fetch users');\n        }\n        const usersData = await usersResponse.json();\n        const processedUsers = usersData.map(user => ({\n          id: user.id,\n          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : []\n        }));\n        setUsers(processedUsers);\n\n        // Fetch Departments\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!departmentsResponse.ok) {\n          throw new Error('Failed to fetch departments');\n        }\n        const departmentsData = await departmentsResponse.json();\n        setDepartments(departmentsData.departments || []);\n\n        // Fetch Teams for duplicate checking\n        const teamsResponse = await fetch(`${API_URL}/teams`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!teamsResponse.ok) {\n          throw new Error('Failed to fetch teams');\n        }\n        const teamsData = await teamsResponse.json();\n        setTeams(teamsData.teams || []);\n      } catch (error) {\n        setError(error.message);\n        console.error('Error fetching ', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (isVisible) {\n      fetchData();\n    }\n  }, [isVisible]);\n\n  // Fetch logged-in user data (user_id)\n  useEffect(() => {\n    const userId = localStorage.getItem('user_id');\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n  const handleSubmit = async event => {\n    event.preventDefault();\n\n    // Get user_id from localStorage for 'created_by'\n    const createdBy = loggedInUser;\n    if (!createdBy) {\n      setError('User is not logged in.');\n      return;\n    }\n    const trimmedTeamName = teamName.trim();\n\n    // Validate required fields\n    if (!trimmedTeamName) {\n      setError('Team name is required.');\n      return;\n    }\n    if (!(selectedDepartment !== null && selectedDepartment !== void 0 && selectedDepartment.value)) {\n      setError('Department is required.');\n      return;\n    }\n    if (!icon) {\n      setError('Icon is required.');\n      return;\n    }\n    if (!logo) {\n      setError('Logo is required.');\n      return;\n    }\n    if (!(selectedPoc !== null && selectedPoc !== void 0 && selectedPoc.value)) {\n      setError('Point of Contact is required.');\n      return;\n    }\n    if (!(selectedManager !== null && selectedManager !== void 0 && selectedManager.value)) {\n      setError('Manager is required.');\n      return;\n    }\n    if (!(selectedTeamLead !== null && selectedTeamLead !== void 0 && selectedTeamLead.value)) {\n      setError('Team Lead is required.');\n      return;\n    }\n\n    // Validate workdays\n    if (workday.length === 0) {\n      setError('At least one workday must be selected.');\n      return;\n    }\n\n    // Check if the team already exists (case insensitive)\n    const teamExists = teams.some(team => team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase());\n    if (teamExists) {\n      setError('Team already exists. Please add a different team.');\n      setTimeout(() => setError(''), 3000);\n      return;\n    }\n    setError(''); // Clear any previous error\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('Authentication token is missing.');\n      }\n      const formData = new FormData();\n      formData.append('name', trimmedTeamName);\n      formData.append('icon', icon);\n      formData.append('logo', logo);\n      formData.append('poc', (selectedPoc === null || selectedPoc === void 0 ? void 0 : selectedPoc.value) || poc);\n      formData.append('manager', (selectedManager === null || selectedManager === void 0 ? void 0 : selectedManager.value) || manager);\n      formData.append('team_lead', (selectedTeamLead === null || selectedTeamLead === void 0 ? void 0 : selectedTeamLead.value) || teamLead);\n      formData.append('workday', JSON.stringify(workday));\n      formData.append('billable_hours', billableHours || '');\n      formData.append('launch', launch || ''); // Allow empty launch date\n      formData.append('department_id', (selectedDepartment === null || selectedDepartment === void 0 ? void 0 : selectedDepartment.value) || departmentId);\n      formData.append('created_by', createdBy);\n      const response = await fetch(`${API_URL}/teams`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData\n      });\n      if (!response.ok) {\n        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;\n        try {\n          // Clone the response to avoid \"body stream already read\" error\n          const responseClone = response.clone();\n          const errorData = await responseClone.json();\n          errorMessage = errorData.error || errorData.message || errorMessage;\n        } catch (parseError) {\n          // If response is not JSON, get text from original response\n          try {\n            const errorText = await response.text();\n            errorMessage = errorText || 'Server returned an error';\n          } catch (textError) {\n            console.error('Could not parse error response:', textError);\n          }\n        }\n        throw new Error(errorMessage);\n      }\n      await response.json();\n      alertMessage('success');\n\n      // Reset form\n      setTeamName('');\n      setIcon(null);\n      setLogo(null);\n      setPoc('');\n      setManager('');\n      setTeamLead('');\n      setLaunch('');\n      setWorkday([]);\n      setBillableHours('');\n      setDepartmentId('');\n      setSelectedDepartment(null);\n      setSelectedPoc(null);\n      setSelectedManager(null);\n      setSelectedTeamLead(null);\n\n      // Close modal\n      setVisible(false);\n    } catch (error) {\n      setError(error.message || 'Failed to add team.');\n      console.error('Error adding team:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Convert workday array to Select options for display\n  const workdayOptions = workday.map(day => ({\n    value: day,\n    label: day\n  }));\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4 bg-gray-100 p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl text-left font-medium text-gray-800\",\n            children: \"Add New Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setVisible(false),\n            className: \"text-3xl text-gray-500 hover:text-gray-800\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"text-left p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"department\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Department *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              options: departmentOptions,\n              value: selectedDepartment,\n              onChange: option => {\n                setSelectedDepartment(option);\n                setDepartmentId((option === null || option === void 0 ? void 0 : option.value) || '');\n              },\n              placeholder: \"Select Department\",\n              className: \"w-full\",\n              isSearchable: true,\n              isDisabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"teamName\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Team Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"teamName\",\n              value: teamName,\n              onChange: e => {\n                setTeamName(e.target.value);\n                if (error) setError(''); // Clear error when user starts typing\n              },\n              required: true,\n              disabled: loading,\n              className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"icon\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Icon *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"icon\",\n              onChange: e => setIcon(e.target.files[0]),\n              accept: \"image/*\",\n              required: true,\n              disabled: loading,\n              className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), icon && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: URL.createObjectURL(icon),\n                alt: \"Icon Preview\",\n                className: \"w-16 h-16 object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"logo\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Logo *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"logo\",\n              onChange: e => setLogo(e.target.files[0]),\n              accept: \"image/*\",\n              required: true,\n              disabled: loading,\n              className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), logo && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: URL.createObjectURL(logo),\n                alt: \"Logo Preview\",\n                className: \"w-32 h-16 object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"poc\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Point of Contact *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              options: pocOptions,\n              value: selectedPoc,\n              onChange: option => {\n                setSelectedPoc(option);\n                setPoc((option === null || option === void 0 ? void 0 : option.value) || '');\n              },\n              placeholder: \"Select POC\",\n              className: \"w-full\",\n              isSearchable: true,\n              isDisabled: loading,\n              noOptionsMessage: () => \"No options available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"manager\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Manager *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              options: managerOptions,\n              value: selectedManager,\n              onChange: option => {\n                setSelectedManager(option);\n                setManager((option === null || option === void 0 ? void 0 : option.value) || '');\n              },\n              placeholder: \"Select Manager\",\n              className: \"w-full\",\n              isSearchable: true,\n              isDisabled: loading,\n              noOptionsMessage: () => \"No managers found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"teamLead\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Team Lead *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              options: teamLeadOptions,\n              value: selectedTeamLead,\n              onChange: option => {\n                setSelectedTeamLead(option);\n                setTeamLead((option === null || option === void 0 ? void 0 : option.value) || '');\n              },\n              placeholder: \"Select Team Lead\",\n              className: \"w-full\",\n              isSearchable: true,\n              isDisabled: loading,\n              noOptionsMessage: () => \"No team leads found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Work Days *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              isMulti: true,\n              options: daysOfWeek,\n              value: workdayOptions,\n              onChange: handleWorkdayChange,\n              placeholder: \"Select Work Days\",\n              className: \"w-full\",\n              isDisabled: loading,\n              noOptionsMessage: () => \"No options available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), workday.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-2\",\n              children: [\"Selected: \", workday.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"billableHours\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Billable Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"billableHours\",\n              value: billableHours,\n              onChange: e => {\n                setBillableHours(e.target.value);\n                if (error) setError(''); // Clear error when user starts typing\n              },\n              min: \"0\",\n              placeholder: \"Enter billable hours\",\n              disabled: loading,\n              className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"launch\",\n              className: \"block text-sm font-medium text-gray-700 pb-4\",\n              children: \"Launch Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"launch\",\n              value: launch,\n              onChange: e => setLaunch(e.target.value),\n              disabled: loading,\n              className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mb-4\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 23\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left pt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: `w-56 py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4 ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary hover:bg-secondary text-white'}`,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-rounded animate-spin text-white text-xl font-regular\",\n                  children: \"progress_activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this), \"Adding...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-rounded text-white text-xl font-regular\",\n                  children: \"add_circle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), \"Add Team\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(AddTeam, \"uZ+nj2NcFbi5eDBCtqqolF+b0qQ=\");\n_c = AddTeam;\nexport default AddTeam;\nvar _c;\n$RefreshReg$(_c, \"AddTeam\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "alertMessage", "Select", "X", "Users", "Building", "User", "Calendar", "Clock", "Upload", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "AddTeam", "isVisible", "setVisible", "_s", "users", "setUsers", "departments", "setDepartments", "teams", "setTeams", "teamName", "setTeamName", "icon", "setIcon", "logo", "set<PERSON><PERSON>", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "launch", "setLaunch", "workday", "setWorkday", "billableHours", "setBillableHours", "departmentId", "setDepartmentId", "error", "setError", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "selectedDepartment", "setSelectedDepartment", "selectedPoc", "setSelectedPoc", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedManager", "selectedTeamLead", "setSelectedTeamLead", "daysOfWeek", "value", "label", "handleWorkdayChange", "selectedOptions", "<PERSON><PERSON><PERSON><PERSON>", "map", "option", "departmentOptions", "dept", "id", "name", "getUserOptions", "allowedRoles", "filter", "user", "_user$resource_types", "hasValidResponsibility", "resource_types", "some", "rt", "includes", "hasValidName", "fullName", "trim", "pocOptions", "managerOptions", "teamLeadOptions", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "processedUsers", "fname", "lname", "roles", "Array", "isArray", "r", "departmentsResponse", "departmentsData", "teamsResponse", "teamsData", "message", "console", "userId", "handleSubmit", "event", "preventDefault", "created<PERSON>y", "trimmedTeamName", "length", "teamExists", "team", "toLowerCase", "setTimeout", "formData", "FormData", "append", "JSON", "stringify", "response", "body", "errorMessage", "status", "statusText", "responseClone", "clone", "errorData", "parseError", "errorText", "text", "textError", "workdayOptions", "day", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "options", "onChange", "placeholder", "isSearchable", "isDisabled", "type", "e", "target", "required", "disabled", "files", "accept", "src", "URL", "createObjectURL", "alt", "noOptionsMessage", "is<PERSON><PERSON><PERSON>", "join", "min", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team/AddTeam.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\nimport Select from 'react-select';\r\nimport {\r\n  X,\r\n  Users,\r\n  Building,\r\n  User,\r\n  Calendar,\r\n  Clock,\r\n  Upload,\r\n  AlertCircle,\r\n  CheckCircle,\r\n} from \"lucide-react\";\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem('token');\r\n  return token !== null;\r\n};\r\n\r\nconst AddTeam = ({ isVisible, setVisible }) => {\r\n  const [users, setUsers] = useState([]);\r\n  const [departments, setDepartments] = useState([]);\r\n  const [teams, setTeams] = useState([]);\r\n  const [teamName, setTeamName] = useState('');\r\n  const [icon, setIcon] = useState(null);\r\n  const [logo, setLogo] = useState(null);\r\n  const [poc, setPoc] = useState('');\r\n  const [manager, setManager] = useState('');\r\n  const [teamLead, setTeamLead] = useState('');\r\n  const [launch, setLaunch] = useState('');\r\n  const [workday, setWorkday] = useState([]);\r\n  const [billableHours, setBillableHours] = useState('');\r\n  const [departmentId, setDepartmentId] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [loggedInUser, setLoggedInUser] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // React Select states\r\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\r\n  const [selectedPoc, setSelectedPoc] = useState(null);\r\n  const [selectedManager, setSelectedManager] = useState(null);\r\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\r\n\r\n  // Days of the week for multi-select\r\n  const daysOfWeek = [\r\n    { value: 'Monday', label: 'Monday' },\r\n    { value: 'Tuesday', label: 'Tuesday' },\r\n    { value: 'Wednesday', label: 'Wednesday' },\r\n    { value: 'Thursday', label: 'Thursday' },\r\n    { value: 'Friday', label: 'Friday' },\r\n    { value: 'Saturday', label: 'Saturday' },\r\n    { value: 'Sunday', label: 'Sunday' }\r\n  ];\r\n\r\n  // Handle workday selection\r\n  const handleWorkdayChange = (selectedOptions) => {\r\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\r\n    setWorkday(selectedValues);\r\n  };\r\n\r\n  // Create options for React Select dropdowns\r\n  const departmentOptions = departments.map(dept => ({\r\n    value: dept.id,\r\n    label: dept.name\r\n  }));\r\n\r\n  // Filter users based on responsibility levels\r\n  const getUserOptions = (allowedRoles) => {\r\n    return users\r\n      .filter(user => {\r\n        // Check if user has any of the allowed roles\r\n        const hasValidResponsibility = user.resource_types?.some(rt => \r\n          allowedRoles.includes(rt)\r\n        );\r\n        // Ensure user has a valid name\r\n        const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n        return hasValidResponsibility && hasValidName;\r\n      })\r\n      .map(user => ({\r\n        value: user.fullName,\r\n        label: user.fullName\r\n      }));\r\n  };\r\n\r\n  // Updated filtering based on responsibility level\r\n  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\r\n  const managerOptions = getUserOptions(['Manager', 'HOD']);\r\n  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!isVisible || !isTokenValid()) {\r\n        return;\r\n      }\r\n\r\n      const token = localStorage.getItem('token');\r\n\r\n      try {\r\n        setLoading(true);\r\n        setError('');\r\n\r\n        // Fetch Users\r\n        const usersResponse = await fetch(`${API_URL}/users`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!usersResponse.ok) {\r\n          throw new Error('Failed to fetch users');\r\n        }\r\n\r\n        const usersData = await usersResponse.json();\r\n        const processedUsers = usersData.map(user => ({\r\n          id: user.id,\r\n          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),\r\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],\r\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : [],\r\n        }));\r\n        setUsers(processedUsers);\r\n\r\n        // Fetch Departments\r\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!departmentsResponse.ok) {\r\n          throw new Error('Failed to fetch departments');\r\n        }\r\n\r\n        const departmentsData = await departmentsResponse.json();\r\n        setDepartments(departmentsData.departments || []);\r\n        \r\n        // Fetch Teams for duplicate checking\r\n        const teamsResponse = await fetch(`${API_URL}/teams`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!teamsResponse.ok) {\r\n          throw new Error('Failed to fetch teams');\r\n        }\r\n\r\n        const teamsData = await teamsResponse.json();\r\n        setTeams(teamsData.teams || []);\r\n      } catch (error) {\r\n        setError(error.message);\r\n        console.error('Error fetching ', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isVisible) {\r\n      fetchData();\r\n    }\r\n  }, [isVisible]);\r\n\r\n  // Fetch logged-in user data (user_id)\r\n  useEffect(() => {\r\n    const userId = localStorage.getItem('user_id');\r\n    if (userId) {\r\n      setLoggedInUser(userId);\r\n    }\r\n  }, []);\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n\r\n    // Get user_id from localStorage for 'created_by'\r\n    const createdBy = loggedInUser;\r\n\r\n    if (!createdBy) {\r\n      setError('User is not logged in.');\r\n      return;\r\n    }\r\n\r\n    const trimmedTeamName = teamName.trim();\r\n\r\n    // Validate required fields\r\n    if (!trimmedTeamName) {\r\n      setError('Team name is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedDepartment?.value) {\r\n      setError('Department is required.');\r\n      return;\r\n    }\r\n\r\n    if (!icon) {\r\n      setError('Icon is required.');\r\n      return;\r\n    }\r\n\r\n    if (!logo) {\r\n      setError('Logo is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedPoc?.value) {\r\n      setError('Point of Contact is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedManager?.value) {\r\n      setError('Manager is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedTeamLead?.value) {\r\n      setError('Team Lead is required.');\r\n      return;\r\n    }\r\n\r\n    // Validate workdays\r\n    if (workday.length === 0) {\r\n      setError('At least one workday must be selected.');\r\n      return;\r\n    }\r\n\r\n    // Check if the team already exists (case insensitive)\r\n    const teamExists = teams.some(team => \r\n      team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase()\r\n    );\r\n\r\n    if (teamExists) {\r\n      setError('Team already exists. Please add a different team.');\r\n      setTimeout(() => setError(''), 3000);\r\n      return;\r\n    }\r\n\r\n    setError(''); // Clear any previous error\r\n    setLoading(true);\r\n\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        throw new Error('Authentication token is missing.');\r\n      }\r\n\r\n      const formData = new FormData();\r\n      formData.append('name', trimmedTeamName);\r\n      formData.append('icon', icon);\r\n      formData.append('logo', logo);\r\n      formData.append('poc', selectedPoc?.value || poc);\r\n      formData.append('manager', selectedManager?.value || manager);\r\n      formData.append('team_lead', selectedTeamLead?.value || teamLead);\r\n      formData.append('workday', JSON.stringify(workday));\r\n      formData.append('billable_hours', billableHours || '');\r\n      formData.append('launch', launch || ''); // Allow empty launch date\r\n      formData.append('department_id', selectedDepartment?.value || departmentId);\r\n      formData.append('created_by', createdBy);\r\n\r\n      const response = await fetch(`${API_URL}/teams`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;\r\n        try {\r\n          // Clone the response to avoid \"body stream already read\" error\r\n          const responseClone = response.clone();\r\n          const errorData = await responseClone.json();\r\n          errorMessage = errorData.error || errorData.message || errorMessage;\r\n        } catch (parseError) {\r\n          // If response is not JSON, get text from original response\r\n          try {\r\n            const errorText = await response.text();\r\n            errorMessage = errorText || 'Server returned an error';\r\n          } catch (textError) {\r\n            console.error('Could not parse error response:', textError);\r\n          }\r\n        }\r\n        throw new Error(errorMessage);\r\n      }\r\n\r\n      await response.json();\r\n      alertMessage('success');\r\n\r\n      // Reset form\r\n      setTeamName('');\r\n      setIcon(null);\r\n      setLogo(null);\r\n      setPoc('');\r\n      setManager('');\r\n      setTeamLead('');\r\n      setLaunch('');\r\n      setWorkday([]);\r\n      setBillableHours('');\r\n      setDepartmentId('');\r\n      setSelectedDepartment(null);\r\n      setSelectedPoc(null);\r\n      setSelectedManager(null);\r\n      setSelectedTeamLead(null);\r\n\r\n      // Close modal\r\n      setVisible(false);\r\n    } catch (error) {\r\n      setError(error.message || 'Failed to add team.');\r\n      console.error('Error adding team:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Convert workday array to Select options for display\r\n  const workdayOptions = workday.map(day => ({\r\n    value: day,\r\n    label: day\r\n  }));\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n        <div className=\"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\">\r\n          <div className=\"flex justify-between items-center mb-4 bg-gray-100 p-4\">\r\n            <h4 className=\"text-xl text-left font-medium text-gray-800\">Add New Team</h4>\r\n            <button \r\n              onClick={() => setVisible(false)}\r\n              className=\"text-3xl text-gray-500 hover:text-gray-800\"\r\n            >\r\n              &times;\r\n            </button>\r\n          </div>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"text-left p-6\">\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Department *\r\n              </label>\r\n              <Select\r\n                options={departmentOptions}\r\n                value={selectedDepartment}\r\n                onChange={(option) => {\r\n                  setSelectedDepartment(option);\r\n                  setDepartmentId(option?.value || '');\r\n                }}\r\n                placeholder=\"Select Department\"\r\n                className=\"w-full\"\r\n                isSearchable\r\n                isDisabled={loading}\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"teamName\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Team Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"teamName\"\r\n                value={teamName}\r\n                onChange={(e) => {\r\n                  setTeamName(e.target.value);\r\n                  if (error) setError(''); // Clear error when user starts typing\r\n                }}\r\n                required\r\n                disabled={loading}\r\n                className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"icon\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Icon *\r\n              </label>\r\n              <input\r\n                type=\"file\"\r\n                id=\"icon\"\r\n                onChange={(e) => setIcon(e.target.files[0])}\r\n                accept=\"image/*\"\r\n                required\r\n                disabled={loading}\r\n                className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n              />\r\n              {icon && (\r\n                <div className=\"mt-2\">\r\n                  <img \r\n                    src={URL.createObjectURL(icon)} \r\n                    alt=\"Icon Preview\" \r\n                    className=\"w-16 h-16 object-contain\"\r\n                  />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"logo\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Logo *\r\n              </label>\r\n              <input\r\n                type=\"file\"\r\n                id=\"logo\"\r\n                onChange={(e) => setLogo(e.target.files[0])}\r\n                accept=\"image/*\"\r\n                required\r\n                disabled={loading}\r\n                className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n              />\r\n              {logo && (\r\n                <div className=\"mt-2\">\r\n                  <img \r\n                    src={URL.createObjectURL(logo)} \r\n                    alt=\"Logo Preview\" \r\n                    className=\"w-32 h-16 object-contain\"\r\n                  />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"poc\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Point of Contact *\r\n              </label>\r\n              <Select\r\n                options={pocOptions}\r\n                value={selectedPoc}\r\n                onChange={(option) => {\r\n                  setSelectedPoc(option);\r\n                  setPoc(option?.value || '');\r\n                }}\r\n                placeholder=\"Select POC\"\r\n                className=\"w-full\"\r\n                isSearchable\r\n                isDisabled={loading}\r\n                noOptionsMessage={() => \"No options available\"}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"manager\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Manager *\r\n              </label>\r\n              <Select\r\n                options={managerOptions}\r\n                value={selectedManager}\r\n                onChange={(option) => {\r\n                  setSelectedManager(option);\r\n                  setManager(option?.value || '');\r\n                }}\r\n                placeholder=\"Select Manager\"\r\n                className=\"w-full\"\r\n                isSearchable\r\n                isDisabled={loading}\r\n                noOptionsMessage={() => \"No managers found\"}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"teamLead\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Team Lead *\r\n              </label>\r\n              <Select\r\n                options={teamLeadOptions}\r\n                value={selectedTeamLead}\r\n                onChange={(option) => {\r\n                  setSelectedTeamLead(option);\r\n                  setTeamLead(option?.value || '');\r\n                }}\r\n                placeholder=\"Select Team Lead\"\r\n                className=\"w-full\"\r\n                isSearchable\r\n                isDisabled={loading}\r\n                noOptionsMessage={() => \"No team leads found\"}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Work Days *\r\n              </label>\r\n              <Select\r\n                isMulti\r\n                options={daysOfWeek}\r\n                value={workdayOptions}\r\n                onChange={handleWorkdayChange}\r\n                placeholder=\"Select Work Days\"\r\n                className=\"w-full\"\r\n                isDisabled={loading}\r\n                noOptionsMessage={() => \"No options available\"}\r\n              />\r\n              {workday.length > 0 && (\r\n                <p className=\"text-xs text-gray-500 mt-2\">\r\n                  Selected: {workday.join(', ')}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"billableHours\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Billable Hours\r\n              </label>\r\n              <input\r\n                type=\"number\"\r\n                id=\"billableHours\"\r\n                value={billableHours}\r\n                onChange={(e) => {\r\n                  setBillableHours(e.target.value);\r\n                  if (error) setError(''); // Clear error when user starts typing\r\n                }}\r\n                min=\"0\"\r\n                placeholder=\"Enter billable hours\"\r\n                disabled={loading}\r\n                className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"launch\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                Launch Date\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                id=\"launch\"\r\n                value={launch}\r\n                onChange={(e) => setLaunch(e.target.value)}\r\n                disabled={loading}\r\n                className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n              />\r\n            </div>\r\n\r\n            {error && <p className=\"text-red-500 text-sm mb-4\">{error}</p>}\r\n\r\n            <div className=\"text-left pt-6\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className={`w-56 py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4 ${\r\n                  loading \r\n                    ? 'bg-gray-400 cursor-not-allowed' \r\n                    : 'bg-primary hover:bg-secondary text-white'\r\n                }`}\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <span className=\"material-symbols-rounded animate-spin text-white text-xl font-regular\">\r\n                      progress_activity\r\n                    </span>\r\n                    Adding...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <span className=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                    Add Team\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddTeam;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,CAAC,EACDC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,MAAMG,OAAO,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwC,GAAG,EAAEC,MAAM,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAAC4D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMoE,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,eAAe,IAAK;IAC/C,MAAMC,cAAc,GAAGD,eAAe,GAAGA,eAAe,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACN,KAAK,CAAC,GAAG,EAAE;IACzFpB,UAAU,CAACwB,cAAc,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAG9C,WAAW,CAAC4C,GAAG,CAACG,IAAI,KAAK;IACjDR,KAAK,EAAEQ,IAAI,CAACC,EAAE;IACdR,KAAK,EAAEO,IAAI,CAACE;EACd,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACvC,OAAOrD,KAAK,CACTsD,MAAM,CAACC,IAAI,IAAI;MAAA,IAAAC,oBAAA;MACd;MACA,MAAMC,sBAAsB,IAAAD,oBAAA,GAAGD,IAAI,CAACG,cAAc,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBG,IAAI,CAACC,EAAE,IACzDP,YAAY,CAACQ,QAAQ,CAACD,EAAE,CAC1B,CAAC;MACD;MACA,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;MACjE,OAAOP,sBAAsB,IAAIK,YAAY;IAC/C,CAAC,CAAC,CACDhB,GAAG,CAACS,IAAI,KAAK;MACZd,KAAK,EAAEc,IAAI,CAACQ,QAAQ;MACpBrB,KAAK,EAAEa,IAAI,CAACQ;IACd,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAME,UAAU,GAAGb,cAAc,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAClE,MAAMc,cAAc,GAAGd,cAAc,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;EACzD,MAAMe,eAAe,GAAGf,cAAc,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAEvEjF,SAAS,CAAC,MAAM;IACd,MAAMiG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACvE,SAAS,IAAI,CAACL,YAAY,CAAC,CAAC,EAAE;QACjC;MACF;MAEA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI;QACFoC,UAAU,CAAC,IAAI,CAAC;QAChBJ,QAAQ,CAAC,EAAE,CAAC;;QAEZ;QACA,MAAM0C,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAGlF,OAAO,QAAQ,EAAE;UACpDmF,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAU/E,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAC4E,aAAa,CAACI,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5C,MAAMC,cAAc,GAAGF,SAAS,CAAC7B,GAAG,CAACS,IAAI,KAAK;UAC5CL,EAAE,EAAEK,IAAI,CAACL,EAAE;UACXa,QAAQ,EAAE,GAAG,CAACR,IAAI,CAACuB,KAAK,IAAI,EAAE,EAAEd,IAAI,CAAC,CAAC,IAAI,CAACT,IAAI,CAACwB,KAAK,IAAI,EAAE,EAAEf,IAAI,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;UAC5EgB,KAAK,EAAEC,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAACyB,KAAK,CAAC,GAAGzB,IAAI,CAACyB,KAAK,CAAClC,GAAG,CAACqC,CAAC,IAAI,CAACA,CAAC,CAAChC,IAAI,IAAI,EAAE,EAAEa,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;UAClFN,cAAc,EAAEuB,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAACG,cAAc,CAAC,GAAGH,IAAI,CAACG,cAAc,CAACZ,GAAG,CAACc,EAAE,IAAI,CAACA,EAAE,CAACT,IAAI,IAAI,EAAE,EAAEa,IAAI,CAAC,CAAC,CAAC,GAAG;QAC/G,CAAC,CAAC,CAAC;QACH/D,QAAQ,CAAC4E,cAAc,CAAC;;QAExB;QACA,MAAMO,mBAAmB,GAAG,MAAMd,KAAK,CAAC,GAAGlF,OAAO,cAAc,EAAE;UAChEmF,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAU/E,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAC2F,mBAAmB,CAACX,EAAE,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,MAAMW,eAAe,GAAG,MAAMD,mBAAmB,CAACR,IAAI,CAAC,CAAC;QACxDzE,cAAc,CAACkF,eAAe,CAACnF,WAAW,IAAI,EAAE,CAAC;;QAEjD;QACA,MAAMoF,aAAa,GAAG,MAAMhB,KAAK,CAAC,GAAGlF,OAAO,QAAQ,EAAE;UACpDmF,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAU/E,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAC6F,aAAa,CAACb,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMa,SAAS,GAAG,MAAMD,aAAa,CAACV,IAAI,CAAC,CAAC;QAC5CvE,QAAQ,CAACkF,SAAS,CAACnF,KAAK,IAAI,EAAE,CAAC;MACjC,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAAC8D,OAAO,CAAC;QACvBC,OAAO,CAAC/D,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACzC,CAAC,SAAS;QACRK,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIlC,SAAS,EAAE;MACbuE,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACvE,SAAS,CAAC,CAAC;;EAEf;EACA1B,SAAS,CAAC,MAAM;IACd,MAAMuH,MAAM,GAAGhG,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAI+F,MAAM,EAAE;MACV7D,eAAe,CAAC6D,MAAM,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAOC,KAAK,IAAK;IACpCA,KAAK,CAACC,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAS,GAAGlE,YAAY;IAE9B,IAAI,CAACkE,SAAS,EAAE;MACdnE,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,MAAMoE,eAAe,GAAGzF,QAAQ,CAAC0D,IAAI,CAAC,CAAC;;IAEvC;IACA,IAAI,CAAC+B,eAAe,EAAE;MACpBpE,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAI,EAACK,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAES,KAAK,GAAE;MAC9Bd,QAAQ,CAAC,yBAAyB,CAAC;MACnC;IACF;IAEA,IAAI,CAACnB,IAAI,EAAE;MACTmB,QAAQ,CAAC,mBAAmB,CAAC;MAC7B;IACF;IAEA,IAAI,CAACjB,IAAI,EAAE;MACTiB,QAAQ,CAAC,mBAAmB,CAAC;MAC7B;IACF;IAEA,IAAI,EAACO,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEO,KAAK,GAAE;MACvBd,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI,EAACS,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEK,KAAK,GAAE;MAC3Bd,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,IAAI,EAACW,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEG,KAAK,GAAE;MAC5Bd,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;;IAEA;IACA,IAAIP,OAAO,CAAC4E,MAAM,KAAK,CAAC,EAAE;MACxBrE,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;;IAEA;IACA,MAAMsE,UAAU,GAAG7F,KAAK,CAACuD,IAAI,CAACuC,IAAI,IAChCA,IAAI,CAAC/C,IAAI,CAACgD,WAAW,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,KAAK+B,eAAe,CAACI,WAAW,CAAC,CACjE,CAAC;IAED,IAAIF,UAAU,EAAE;MACdtE,QAAQ,CAAC,mDAAmD,CAAC;MAC7DyE,UAAU,CAAC,MAAMzE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACpC;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdI,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMtC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIiF,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAER,eAAe,CAAC;MACxCM,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/F,IAAI,CAAC;MAC7B6F,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE7F,IAAI,CAAC;MAC7B2F,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAE,CAAArE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,KAAK,KAAI7B,GAAG,CAAC;MACjDyF,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE,CAAAnE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,KAAK,KAAI3B,OAAO,CAAC;MAC7DuF,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE,CAAAjE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEG,KAAK,KAAIzB,QAAQ,CAAC;MACjEqF,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACrF,OAAO,CAAC,CAAC;MACnDiF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEjF,aAAa,IAAI,EAAE,CAAC;MACtD+E,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAErF,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;MACzCmF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE,CAAAvE,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAES,KAAK,KAAIjB,YAAY,CAAC;MAC3E6E,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAET,SAAS,CAAC;MAExC,MAAMY,QAAQ,GAAG,MAAMpC,KAAK,CAAC,GAAGlF,OAAO,QAAQ,EAAE;QAC/CmF,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU/E,KAAK;QAClC,CAAC;QACDkH,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACK,QAAQ,CAACjC,EAAE,EAAE;QAChB,IAAImC,YAAY,GAAG,wBAAwBF,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACI,UAAU,EAAE;QACnF,IAAI;UACF;UACA,MAAMC,aAAa,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC;UACtC,MAAMC,SAAS,GAAG,MAAMF,aAAa,CAACnC,IAAI,CAAC,CAAC;UAC5CgC,YAAY,GAAGK,SAAS,CAACvF,KAAK,IAAIuF,SAAS,CAACzB,OAAO,IAAIoB,YAAY;QACrE,CAAC,CAAC,OAAOM,UAAU,EAAE;UACnB;UACA,IAAI;YACF,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;YACvCR,YAAY,GAAGO,SAAS,IAAI,0BAA0B;UACxD,CAAC,CAAC,OAAOE,SAAS,EAAE;YAClB5B,OAAO,CAAC/D,KAAK,CAAC,iCAAiC,EAAE2F,SAAS,CAAC;UAC7D;QACF;QACA,MAAM,IAAI3C,KAAK,CAACkC,YAAY,CAAC;MAC/B;MAEA,MAAMF,QAAQ,CAAC9B,IAAI,CAAC,CAAC;MACrBvG,YAAY,CAAC,SAAS,CAAC;;MAEvB;MACAkC,WAAW,CAAC,EAAE,CAAC;MACfE,OAAO,CAAC,IAAI,CAAC;MACbE,OAAO,CAAC,IAAI,CAAC;MACbE,MAAM,CAAC,EAAE,CAAC;MACVE,UAAU,CAAC,EAAE,CAAC;MACdE,WAAW,CAAC,EAAE,CAAC;MACfE,SAAS,CAAC,EAAE,CAAC;MACbE,UAAU,CAAC,EAAE,CAAC;MACdE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBQ,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,IAAI,CAAC;MACxBE,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAzC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC8D,OAAO,IAAI,qBAAqB,CAAC;MAChDC,OAAO,CAAC/D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuF,cAAc,GAAGlG,OAAO,CAAC0B,GAAG,CAACyE,GAAG,KAAK;IACzC9E,KAAK,EAAE8E,GAAG;IACV7E,KAAK,EAAE6E;EACT,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC1H,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEZ,OAAA,CAAAE,SAAA;IAAAqI,QAAA,eACEvI,OAAA;MAAKwI,SAAS,EAAC,kHAAkH;MAAAD,QAAA,eAC/HvI,OAAA;QAAKwI,SAAS,EAAC,2GAA2G;QAAAD,QAAA,gBACxHvI,OAAA;UAAKwI,SAAS,EAAC,wDAAwD;UAAAD,QAAA,gBACrEvI,OAAA;YAAIwI,SAAS,EAAC,6CAA6C;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7E5I,OAAA;YACE6I,OAAO,EAAEA,CAAA,KAAMhI,UAAU,CAAC,KAAK,CAAE;YACjC2H,SAAS,EAAC,4CAA4C;YAAAD,QAAA,EACvD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5I,OAAA;UAAM8I,QAAQ,EAAEpC,YAAa;UAAC8B,SAAS,EAAC,eAAe;UAAAD,QAAA,gBACrDvI,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,YAAY;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAErF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA,CAACX,MAAM;cACL2J,OAAO,EAAEjF,iBAAkB;cAC3BP,KAAK,EAAET,kBAAmB;cAC1BkG,QAAQ,EAAGnF,MAAM,IAAK;gBACpBd,qBAAqB,CAACc,MAAM,CAAC;gBAC7BtB,eAAe,CAAC,CAAAsB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;cACtC,CAAE;cACF0F,WAAW,EAAC,mBAAmB;cAC/BV,SAAS,EAAC,QAAQ;cAClBW,YAAY;cACZC,UAAU,EAAEvG;YAAQ;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA;cACEqJ,IAAI,EAAC,MAAM;cACXpF,EAAE,EAAC,UAAU;cACbT,KAAK,EAAEnC,QAAS;cAChB4H,QAAQ,EAAGK,CAAC,IAAK;gBACfhI,WAAW,CAACgI,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAC;gBAC3B,IAAIf,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;cAC3B,CAAE;cACF8G,QAAQ;cACRC,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE/E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA;cACEqJ,IAAI,EAAC,MAAM;cACXpF,EAAE,EAAC,MAAM;cACTgF,QAAQ,EAAGK,CAAC,IAAK9H,OAAO,CAAC8H,CAAC,CAACC,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAE;cAC5CC,MAAM,EAAC,SAAS;cAChBH,QAAQ;cACRC,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC,EACDrH,IAAI,iBACHvB,OAAA;cAAKwI,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBvI,OAAA;gBACE4J,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACvI,IAAI,CAAE;gBAC/BwI,GAAG,EAAC,cAAc;gBAClBvB,SAAS,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE/E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA;cACEqJ,IAAI,EAAC,MAAM;cACXpF,EAAE,EAAC,MAAM;cACTgF,QAAQ,EAAGK,CAAC,IAAK5H,OAAO,CAAC4H,CAAC,CAACC,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAE;cAC5CC,MAAM,EAAC,SAAS;cAChBH,QAAQ;cACRC,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC,EACDnH,IAAI,iBACHzB,OAAA;cAAKwI,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBvI,OAAA;gBACE4J,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACrI,IAAI,CAAE;gBAC/BsI,GAAG,EAAC,cAAc;gBAClBvB,SAAS,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,KAAK;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAE9E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA,CAACX,MAAM;cACL2J,OAAO,EAAEhE,UAAW;cACpBxB,KAAK,EAAEP,WAAY;cACnBgG,QAAQ,EAAGnF,MAAM,IAAK;gBACpBZ,cAAc,CAACY,MAAM,CAAC;gBACtBlC,MAAM,CAAC,CAAAkC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;cAC7B,CAAE;cACF0F,WAAW,EAAC,YAAY;cACxBV,SAAS,EAAC,QAAQ;cAClBW,YAAY;cACZC,UAAU,EAAEvG,OAAQ;cACpBmH,gBAAgB,EAAEA,CAAA,KAAM;YAAuB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAElF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA,CAACX,MAAM;cACL2J,OAAO,EAAE/D,cAAe;cACxBzB,KAAK,EAAEL,eAAgB;cACvB8F,QAAQ,EAAGnF,MAAM,IAAK;gBACpBV,kBAAkB,CAACU,MAAM,CAAC;gBAC1BhC,UAAU,CAAC,CAAAgC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;cACjC,CAAE;cACF0F,WAAW,EAAC,gBAAgB;cAC5BV,SAAS,EAAC,QAAQ;cAClBW,YAAY;cACZC,UAAU,EAAEvG,OAAQ;cACpBmH,gBAAgB,EAAEA,CAAA,KAAM;YAAoB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA,CAACX,MAAM;cACL2J,OAAO,EAAE9D,eAAgB;cACzB1B,KAAK,EAAEH,gBAAiB;cACxB4F,QAAQ,EAAGnF,MAAM,IAAK;gBACpBR,mBAAmB,CAACQ,MAAM,CAAC;gBAC3B9B,WAAW,CAAC,CAAA8B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;cAClC,CAAE;cACF0F,WAAW,EAAC,kBAAkB;cAC9BV,SAAS,EAAC,QAAQ;cAClBW,YAAY;cACZC,UAAU,EAAEvG,OAAQ;cACpBmH,gBAAgB,EAAEA,CAAA,KAAM;YAAsB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAOwI,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA,CAACX,MAAM;cACL4K,OAAO;cACPjB,OAAO,EAAEzF,UAAW;cACpBC,KAAK,EAAE6E,cAAe;cACtBY,QAAQ,EAAEvF,mBAAoB;cAC9BwF,WAAW,EAAC,kBAAkB;cAC9BV,SAAS,EAAC,QAAQ;cAClBY,UAAU,EAAEvG,OAAQ;cACpBmH,gBAAgB,EAAEA,CAAA,KAAM;YAAuB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EACDzG,OAAO,CAAC4E,MAAM,GAAG,CAAC,iBACjB/G,OAAA;cAAGwI,SAAS,EAAC,4BAA4B;cAAAD,QAAA,GAAC,YAC9B,EAACpG,OAAO,CAAC+H,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,eAAe;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAExF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA;cACEqJ,IAAI,EAAC,QAAQ;cACbpF,EAAE,EAAC,eAAe;cAClBT,KAAK,EAAEnB,aAAc;cACrB4G,QAAQ,EAAGK,CAAC,IAAK;gBACfhH,gBAAgB,CAACgH,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAC;gBAChC,IAAIf,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;cAC3B,CAAE;cACFyH,GAAG,EAAC,GAAG;cACPjB,WAAW,EAAC,sBAAsB;cAClCO,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5I,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvI,OAAA;cAAO+I,OAAO,EAAC,QAAQ;cAACP,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5I,OAAA;cACEqJ,IAAI,EAAC,MAAM;cACXpF,EAAE,EAAC,QAAQ;cACXT,KAAK,EAAEvB,MAAO;cACdgH,QAAQ,EAAGK,CAAC,IAAKpH,SAAS,CAACoH,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAC3CiG,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELnG,KAAK,iBAAIzC,OAAA;YAAGwI,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAE9F;UAAK;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE9D5I,OAAA;YAAKwI,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7BvI,OAAA;cACEqJ,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAE5G,OAAQ;cAClB2F,SAAS,EAAE,sFACT3F,OAAO,GACH,gCAAgC,GAChC,0CAA0C,EAC7C;cAAA0F,QAAA,EAEF1F,OAAO,gBACN7C,OAAA,CAAAE,SAAA;gBAAAqI,QAAA,gBACEvI,OAAA;kBAAMwI,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAAC;gBAExF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,aAET;cAAA,eAAE,CAAC,gBAEH5I,OAAA,CAAAE,SAAA;gBAAAqI,QAAA,gBACEvI,OAAA;kBAAMwI,SAAS,EAAC,0DAA0D;kBAAAD,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,YAE9F;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP,CAAC;AAAC9H,EAAA,CAtiBIH,OAAO;AAAAyJ,EAAA,GAAPzJ,OAAO;AAwiBb,eAAeA,OAAO;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}