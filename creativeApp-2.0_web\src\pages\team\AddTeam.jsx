import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';
import Select from 'react-select';
import {
  X,
  Users,
  Building,
  User,
  Calendar,
  Clock,
  Upload,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

const AddTeam = ({ isVisible, setVisible }) => {
  const [users, setUsers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [teams, setTeams] = useState([]);
  const [teamName, setTeamName] = useState('');
  const [icon, setIcon] = useState(null);
  const [logo, setLogo] = useState(null);
  const [poc, setPoc] = useState('');
  const [manager, setManager] = useState('');
  const [teamLead, setTeamLead] = useState('');
  const [launch, setLaunch] = useState('');
  const [workday, setWorkday] = useState([]);
  const [billableHours, setBillableHours] = useState('');
  const [dailyBillableHours, setDailyBillableHours] = useState('');
  const [weeklyBillableHours, setWeeklyBillableHours] = useState('');
  const [departmentId, setDepartmentId] = useState('');
  const [error, setError] = useState('');
  const [errors, setErrors] = useState({});
  const [formError, setFormError] = useState('');
  const [focusedField, setFocusedField] = useState(null);
  const [loggedInUser, setLoggedInUser] = useState(null);
  const [loading, setLoading] = useState(false);

  // React Select states
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedPoc, setSelectedPoc] = useState(null);
  const [selectedManager, setSelectedManager] = useState(null);
  const [selectedTeamLead, setSelectedTeamLead] = useState(null);

  // Days of the week for multi-select
  const daysOfWeek = [
    { value: 'Monday', label: 'Monday' },
    { value: 'Tuesday', label: 'Tuesday' },
    { value: 'Wednesday', label: 'Wednesday' },
    { value: 'Thursday', label: 'Thursday' },
    { value: 'Friday', label: 'Friday' },
    { value: 'Saturday', label: 'Saturday' },
    { value: 'Sunday', label: 'Sunday' }
  ];

  // Handle workday selection
  const handleWorkdayChange = (selectedOptions) => {
    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];
    setWorkday(selectedValues);
  };

  // Create options for React Select dropdowns
  const departmentOptions = departments.map(dept => ({
    value: dept.id,
    label: dept.name
  }));

  // Filter users based on responsibility levels
  const getUserOptions = (allowedRoles) => {
    return users
      .filter(user => {
        // Check if user has any of the allowed roles
        const hasValidResponsibility = user.resource_types?.some(rt => 
          allowedRoles.includes(rt)
        );
        // Ensure user has a valid name
        const hasValidName = user.fullName && user.fullName.trim() !== '';
        return hasValidResponsibility && hasValidName;
      })
      .map(user => ({
        value: user.fullName,
        label: user.fullName
      }));
  };

  // Updated filtering based on responsibility level
  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);
  const managerOptions = getUserOptions(['Manager', 'HOD']);
  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);

  useEffect(() => {
    const fetchData = async () => {
      if (!isVisible || !isTokenValid()) {
        return;
      }

      const token = localStorage.getItem('token');

      try {
        setLoading(true);
        setError('');

        // Fetch Users
        const usersResponse = await fetch(`${API_URL}/users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!usersResponse.ok) {
          throw new Error('Failed to fetch users');
        }

        const usersData = await usersResponse.json();
        const processedUsers = usersData.map(user => ({
          id: user.id,
          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),
          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],
          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : [],
        }));
        setUsers(processedUsers);

        // Fetch Departments
        const departmentsResponse = await fetch(`${API_URL}/departments`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!departmentsResponse.ok) {
          throw new Error('Failed to fetch departments');
        }

        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData.departments || []);
        
        // Fetch Teams for duplicate checking
        const teamsResponse = await fetch(`${API_URL}/teams`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!teamsResponse.ok) {
          throw new Error('Failed to fetch teams');
        }

        const teamsData = await teamsResponse.json();
        setTeams(teamsData.teams || []);
      } catch (error) {
        setError(error.message);
        console.error('Error fetching ', error);
      } finally {
        setLoading(false);
      }
    };

    if (isVisible) {
      fetchData();
    }
  }, [isVisible]);

  // Fetch logged-in user data (user_id)
  useEffect(() => {
    const userId = localStorage.getItem('user_id');
    if (userId) {
      setLoggedInUser(userId);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Get user_id from localStorage for 'created_by'
    const createdBy = loggedInUser;

    if (!createdBy) {
      setError('User is not logged in.');
      return;
    }

    const trimmedTeamName = teamName.trim();

    // Validate required fields
    if (!trimmedTeamName) {
      setError('Team name is required.');
      return;
    }

    if (!selectedDepartment?.value) {
      setError('Department is required.');
      return;
    }

    if (!icon) {
      setError('Icon is required.');
      return;
    }

    if (!logo) {
      setError('Logo is required.');
      return;
    }

    if (!selectedPoc?.value) {
      setError('Point of Contact is required.');
      return;
    }

    if (!selectedManager?.value) {
      setError('Manager is required.');
      return;
    }

    if (!selectedTeamLead?.value) {
      setError('Team Lead is required.');
      return;
    }

    // Validate workdays
    if (workday.length === 0) {
      setError('At least one workday must be selected.');
      return;
    }

    // Check if the team already exists (case insensitive)
    const teamExists = teams.some(team => 
      team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase()
    );

    if (teamExists) {
      setError('Team already exists. Please add a different team.');
      setTimeout(() => setError(''), 3000);
      return;
    }

    setError(''); // Clear any previous error
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token is missing.');
      }

      const formData = new FormData();
      formData.append('name', trimmedTeamName);
      formData.append('icon', icon);
      formData.append('logo', logo);
      formData.append('poc', selectedPoc?.value || poc);
      formData.append('manager', selectedManager?.value || manager);
      formData.append('team_lead', selectedTeamLead?.value || teamLead);
      formData.append('workday', JSON.stringify(workday));
      formData.append('billable_hours', billableHours || '');
      formData.append('daily_billable_hours', dailyBillableHours || '');
      formData.append('weekly_billable_hours', weeklyBillableHours || '');
      formData.append('launch', launch || ''); // Allow empty launch date
      formData.append('department_id', selectedDepartment?.value || departmentId);
      formData.append('created_by', createdBy);

      const response = await fetch(`${API_URL}/teams`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;
        try {
          // Clone the response to avoid "body stream already read" error
          const responseClone = response.clone();
          const errorData = await responseClone.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (parseError) {
          // If response is not JSON, get text from original response
          try {
            const errorText = await response.text();
            errorMessage = errorText || 'Server returned an error';
          } catch (textError) {
            console.error('Could not parse error response:', textError);
          }
        }
        throw new Error(errorMessage);
      }

      await response.json();
      alertMessage('success');

      // Reset form
      setTeamName('');
      setIcon(null);
      setLogo(null);
      setPoc('');
      setManager('');
      setTeamLead('');
      setLaunch('');
      setWorkday([]);
      setBillableHours('');
      setDailyBillableHours('');
      setWeeklyBillableHours('');
      setDepartmentId('');
      setSelectedDepartment(null);
      setSelectedPoc(null);
      setSelectedManager(null);
      setSelectedTeamLead(null);

      // Close modal
      setVisible(false);
    } catch (error) {
      setError(error.message || 'Failed to add team.');
      console.error('Error adding team:', error);
    } finally {
      setLoading(false);
    }
  };

  // Convert workday array to Select options for display
  const workdayOptions = workday.map(day => ({
    value: day,
    label: day
  }));

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500">
        {/* Header - Responsive */}
        <div className="bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                <Users className="w-4 h-4 sm:w-6 sm:h-6" />
              </div>
              <div>
                <div className="text-left">
                  <h2 className="text-lg sm:text-xl font-semibold">
                    Add New Team
                  </h2>
                  <p className="text-white-100 text-xs sm:text-sm">
                    Create a new team for your organization
                  </p>
                </div>
              </div>
            </div>
            <button
              onClick={() => setVisible(false)}
              className="p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </div>
        {/* Form Content - Responsive */}
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]">
          {/* Error Message - Responsive */}
          {formError && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2">
              <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-left font-medium text-red-800 text-sm sm:text-base">
                  Action Required
                </p>
                <p className="text-red-700 text-xs sm:text-sm">{formError}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Responsive Grid Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Department & Team Info Section */}
              <div className="space-y-3 sm:space-y-4">
                <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                    <Building className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                    <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                      Organization
                    </h3>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Department <span className="text-red-500">*</span>
                      </label>
                      <Select
                        options={departmentOptions}
                        value={selectedDepartment}
                        onChange={(option) => {
                          setSelectedDepartment(option);
                          setDepartmentId(option?.value || '');
                        }}
                        placeholder="Select Department"
                        className="w-full"
                        isSearchable
                        isDisabled={loading}
                        styles={{
                          control: (base, state) => ({
                            ...base,
                            borderRadius: '0.5rem',
                            borderWidth: '2px',
                            borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                            boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                            '&:hover': {
                              borderColor: '#D1D5DB'
                            }
                          })
                        }}
                      />
                    </div>

                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Team Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={teamName}
                        onChange={(e) => {
                          setTeamName(e.target.value);
                          if (error) setError('');
                        }}
                        onFocus={() => setFocusedField("teamName")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="Enter team name"
                        disabled={loading}
                        className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                          focusedField === "teamName"
                            ? "border-primary-500 shadow-lg shadow-primary/10-100"
                            : "border-gray-200 focus:border-primary hover:border-gray-300"
                        }`}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Team Assets Section */}
              <div className="space-y-3 sm:space-y-4">
                <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                    <Upload className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                    <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                      Team Assets
                    </h3>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Team Logo <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="file"
                        onChange={(e) => setLogo(e.target.files[0])}
                        accept="image/*"
                        disabled={loading}
                        className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300"
                      />
                      {logo && (
                        <div className="mt-2">
                          <img
                            src={URL.createObjectURL(logo)}
                            alt="Logo Preview"
                            className="w-32 h-16 object-contain rounded-lg border border-gray-200"
                          />
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        Team Icon <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="file"
                        onChange={(e) => setIcon(e.target.files[0])}
                        accept="image/*"
                        disabled={loading}
                        className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300"
                      />
                      {icon && (
                        <div className="mt-2">
                          <img
                            src={URL.createObjectURL(icon)}
                            alt="Icon Preview"
                            className="w-16 h-16 object-contain rounded-lg border border-gray-200"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Team Members Section */}
            <div className="mt-4 sm:mt-6">
              <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
                <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                  <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                    Team Members
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4">
                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Point of Contact <span className="text-red-500">*</span>
                    </label>
                    <Select
                      options={pocOptions}
                      value={selectedPoc}
                      onChange={(option) => {
                        setSelectedPoc(option);
                        setPoc(option?.value || '');
                      }}
                      placeholder="Select POC"
                      className="w-full"
                      isSearchable
                      isDisabled={loading}
                      noOptionsMessage={() => "No options available"}
                      styles={{
                        control: (base, state) => ({
                          ...base,
                          borderRadius: '0.5rem',
                          borderWidth: '2px',
                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                          '&:hover': {
                            borderColor: '#D1D5DB'
                          }
                        })
                      }}
                    />
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Manager <span className="text-red-500">*</span>
                    </label>
                    <Select
                      options={managerOptions}
                      value={selectedManager}
                      onChange={(option) => {
                        setSelectedManager(option);
                        setManager(option?.value || '');
                      }}
                      placeholder="Select Manager"
                      className="w-full"
                      isSearchable
                      isDisabled={loading}
                      noOptionsMessage={() => "No managers found"}
                      styles={{
                        control: (base, state) => ({
                          ...base,
                          borderRadius: '0.5rem',
                          borderWidth: '2px',
                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                          '&:hover': {
                            borderColor: '#D1D5DB'
                          }
                        })
                      }}
                    />
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Team Lead <span className="text-red-500">*</span>
                    </label>
                    <Select
                      options={teamLeadOptions}
                      value={selectedTeamLead}
                      onChange={(option) => {
                        setSelectedTeamLead(option);
                        setTeamLead(option?.value || '');
                      }}
                      placeholder="Select Team Lead"
                      className="w-full"
                      isSearchable
                      isDisabled={loading}
                      noOptionsMessage={() => "No team leads found"}
                      styles={{
                        control: (base, state) => ({
                          ...base,
                          borderRadius: '0.5rem',
                          borderWidth: '2px',
                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                          '&:hover': {
                            borderColor: '#D1D5DB'
                          }
                        })
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Schedule and Billing Section */}
            <div className="mt-4 sm:mt-6">
              <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
                <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                    Schedule & Billing
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Work Days <span className="text-red-500">*</span>
                    </label>
                    <Select
                      isMulti
                      options={daysOfWeek}
                      value={workdayOptions}
                      onChange={handleWorkdayChange}
                      placeholder="Select Work Days"
                      className="w-full"
                      isDisabled={loading}
                      noOptionsMessage={() => "No options available"}
                      styles={{
                        control: (base, state) => ({
                          ...base,
                          borderRadius: '0.5rem',
                          borderWidth: '2px',
                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',
                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',
                          '&:hover': {
                            borderColor: '#D1D5DB'
                          }
                        })
                      }}
                    />
                    {workday.length > 0 && (
                      <p className="text-xs text-gray-500 mt-2">
                        Selected: {workday.join(', ')}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Launch Date
                    </label>
                    <input
                      type="date"
                      value={launch}
                      onChange={(e) => {
                        setLaunch(e.target.value);
                        if (error) setError('');
                      }}
                      onFocus={() => setFocusedField("launch")}
                      onBlur={() => setFocusedField(null)}
                      disabled={loading}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        focusedField === "launch"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4 mt-3 sm:mt-4">
                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Billable Hours
                    </label>
                    <input
                      type="number"
                      value={billableHours}
                      onChange={(e) => {
                        setBillableHours(e.target.value);
                        if (error) setError('');
                      }}
                      onFocus={() => setFocusedField("billableHours")}
                      onBlur={() => setFocusedField(null)}
                      min="0"
                      placeholder="Enter hours"
                      disabled={loading}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        focusedField === "billableHours"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Daily Billable Hours
                    </label>
                    <input
                      type="number"
                      value={dailyBillableHours}
                      onChange={(e) => {
                        setDailyBillableHours(e.target.value);
                        if (error) setError('');
                      }}
                      onFocus={() => setFocusedField("dailyBillableHours")}
                      onBlur={() => setFocusedField(null)}
                      min="0"
                      placeholder="Enter daily hours"
                      disabled={loading}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        focusedField === "dailyBillableHours"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Weekly Billable Hours
                    </label>
                    <input
                      type="number"
                      value={weeklyBillableHours}
                      onChange={(e) => {
                        setWeeklyBillableHours(e.target.value);
                        if (error) setError('');
                      }}
                      onFocus={() => setFocusedField("weeklyBillableHours")}
                      onBlur={() => setFocusedField(null)}
                      min="0"
                      placeholder="Enter weekly hours"
                      disabled={loading}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        focusedField === "weeklyBillableHours"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3">
                <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-left font-medium text-red-800 text-sm sm:text-base">
                    Error
                  </p>
                  <p className="text-red-700 text-xs sm:text-sm">{error}</p>
                </div>
              </div>
            )}

            {/* Submit Button Section */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-4 sm:pt-6 border-t border-gray-200 mt-6">
              <button
                type="button"
                onClick={() => setVisible(false)}
                disabled={loading}
                className="flex-1 px-4 sm:px-6 py-2.5 sm:py-3 border-2 border-gray-300 text-gray-700 rounded-lg sm:rounded-xl font-medium hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-200 transition-all duration-200 text-sm sm:text-base"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`flex-1 px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-medium transition-all duration-200 text-sm sm:text-base flex items-center justify-center space-x-2 ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-primary hover:bg-primary-600 text-white focus:outline-none focus:ring-4 focus:ring-primary/20'
                }`}
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Adding Team...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span>Add Team</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddTeam;