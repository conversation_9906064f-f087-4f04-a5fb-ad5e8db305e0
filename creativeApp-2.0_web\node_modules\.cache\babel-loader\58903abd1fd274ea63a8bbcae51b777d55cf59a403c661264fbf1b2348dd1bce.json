{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\AddTeam.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { alertMessage } from '../../common/coreui';\nimport Select from 'react-select';\nimport { X, Users, Building, User, Calendar, Clock, Upload, AlertCircle, CheckCircle } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst isTokenValid = () => {\n  const token = localStorage.getItem('token');\n  return token !== null;\n};\nconst AddTeam = ({\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [teamName, setTeamName] = useState('');\n  const [icon, setIcon] = useState(null);\n  const [logo, setLogo] = useState(null);\n  const [poc, setPoc] = useState('');\n  const [manager, setManager] = useState('');\n  const [teamLead, setTeamLead] = useState('');\n  const [launch, setLaunch] = useState('');\n  const [workday, setWorkday] = useState([]);\n  const [billableHours, setBillableHours] = useState('');\n  const [dailyBillableHours, setDailyBillableHours] = useState('');\n  const [weeklyBillableHours, setWeeklyBillableHours] = useState('');\n  const [departmentId, setDepartmentId] = useState('');\n  const [error, setError] = useState('');\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState('');\n  const [focusedField, setFocusedField] = useState(null);\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // React Select states\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\n  const [selectedPoc, setSelectedPoc] = useState(null);\n  const [selectedManager, setSelectedManager] = useState(null);\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\n\n  // Days of the week for multi-select\n  const daysOfWeek = [{\n    value: 'Monday',\n    label: 'Monday'\n  }, {\n    value: 'Tuesday',\n    label: 'Tuesday'\n  }, {\n    value: 'Wednesday',\n    label: 'Wednesday'\n  }, {\n    value: 'Thursday',\n    label: 'Thursday'\n  }, {\n    value: 'Friday',\n    label: 'Friday'\n  }, {\n    value: 'Saturday',\n    label: 'Saturday'\n  }, {\n    value: 'Sunday',\n    label: 'Sunday'\n  }];\n\n  // Handle workday selection\n  const handleWorkdayChange = selectedOptions => {\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\n    setWorkday(selectedValues);\n  };\n\n  // Create options for React Select dropdowns\n  const departmentOptions = departments.map(dept => ({\n    value: dept.id,\n    label: dept.name\n  }));\n\n  // Filter users based on responsibility levels\n  const getUserOptions = allowedRoles => {\n    return users.filter(user => {\n      var _user$resource_types;\n      // Check if user has any of the allowed roles\n      const hasValidResponsibility = (_user$resource_types = user.resource_types) === null || _user$resource_types === void 0 ? void 0 : _user$resource_types.some(rt => allowedRoles.includes(rt));\n      // Ensure user has a valid name\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\n      return hasValidResponsibility && hasValidName;\n    }).map(user => ({\n      value: user.fullName,\n      label: user.fullName\n    }));\n  };\n\n  // Updated filtering based on responsibility level\n  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\n  const managerOptions = getUserOptions(['Manager', 'HOD']);\n  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!isVisible || !isTokenValid()) {\n        return;\n      }\n      const token = localStorage.getItem('token');\n      try {\n        setLoading(true);\n        setError('');\n\n        // Fetch Users\n        const usersResponse = await fetch(`${API_URL}/users`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!usersResponse.ok) {\n          throw new Error('Failed to fetch users');\n        }\n        const usersData = await usersResponse.json();\n        const processedUsers = usersData.map(user => ({\n          id: user.id,\n          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : []\n        }));\n        setUsers(processedUsers);\n\n        // Fetch Departments\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!departmentsResponse.ok) {\n          throw new Error('Failed to fetch departments');\n        }\n        const departmentsData = await departmentsResponse.json();\n        setDepartments(departmentsData.departments || []);\n\n        // Fetch Teams for duplicate checking\n        const teamsResponse = await fetch(`${API_URL}/teams`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!teamsResponse.ok) {\n          throw new Error('Failed to fetch teams');\n        }\n        const teamsData = await teamsResponse.json();\n        setTeams(teamsData.teams || []);\n      } catch (error) {\n        setError(error.message);\n        console.error('Error fetching ', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (isVisible) {\n      fetchData();\n    }\n  }, [isVisible]);\n\n  // Fetch logged-in user data (user_id)\n  useEffect(() => {\n    const userId = localStorage.getItem('user_id');\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n  const handleSubmit = async event => {\n    event.preventDefault();\n\n    // Get user_id from localStorage for 'created_by'\n    const createdBy = loggedInUser;\n    if (!createdBy) {\n      setError('User is not logged in.');\n      return;\n    }\n    const trimmedTeamName = teamName.trim();\n\n    // Validate required fields\n    if (!trimmedTeamName) {\n      setError('Team name is required.');\n      return;\n    }\n    if (!(selectedDepartment !== null && selectedDepartment !== void 0 && selectedDepartment.value)) {\n      setError('Department is required.');\n      return;\n    }\n    if (!icon) {\n      setError('Icon is required.');\n      return;\n    }\n    if (!logo) {\n      setError('Logo is required.');\n      return;\n    }\n    if (!(selectedPoc !== null && selectedPoc !== void 0 && selectedPoc.value)) {\n      setError('Point of Contact is required.');\n      return;\n    }\n    if (!(selectedManager !== null && selectedManager !== void 0 && selectedManager.value)) {\n      setError('Manager is required.');\n      return;\n    }\n    if (!(selectedTeamLead !== null && selectedTeamLead !== void 0 && selectedTeamLead.value)) {\n      setError('Team Lead is required.');\n      return;\n    }\n\n    // Validate workdays\n    if (workday.length === 0) {\n      setError('At least one workday must be selected.');\n      return;\n    }\n\n    // Check if the team already exists (case insensitive)\n    const teamExists = teams.some(team => team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase());\n    if (teamExists) {\n      setError('Team already exists. Please add a different team.');\n      setTimeout(() => setError(''), 3000);\n      return;\n    }\n    setError(''); // Clear any previous error\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('Authentication token is missing.');\n      }\n      const formData = new FormData();\n      formData.append('name', trimmedTeamName);\n      formData.append('icon', icon);\n      formData.append('logo', logo);\n      formData.append('poc', (selectedPoc === null || selectedPoc === void 0 ? void 0 : selectedPoc.value) || poc);\n      formData.append('manager', (selectedManager === null || selectedManager === void 0 ? void 0 : selectedManager.value) || manager);\n      formData.append('team_lead', (selectedTeamLead === null || selectedTeamLead === void 0 ? void 0 : selectedTeamLead.value) || teamLead);\n      formData.append('workday', JSON.stringify(workday));\n      formData.append('billable_hours', billableHours || '');\n      formData.append('launch', launch || ''); // Allow empty launch date\n      formData.append('department_id', (selectedDepartment === null || selectedDepartment === void 0 ? void 0 : selectedDepartment.value) || departmentId);\n      formData.append('created_by', createdBy);\n      const response = await fetch(`${API_URL}/teams`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData\n      });\n      if (!response.ok) {\n        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;\n        try {\n          // Clone the response to avoid \"body stream already read\" error\n          const responseClone = response.clone();\n          const errorData = await responseClone.json();\n          errorMessage = errorData.error || errorData.message || errorMessage;\n        } catch (parseError) {\n          // If response is not JSON, get text from original response\n          try {\n            const errorText = await response.text();\n            errorMessage = errorText || 'Server returned an error';\n          } catch (textError) {\n            console.error('Could not parse error response:', textError);\n          }\n        }\n        throw new Error(errorMessage);\n      }\n      await response.json();\n      alertMessage('success');\n\n      // Reset form\n      setTeamName('');\n      setIcon(null);\n      setLogo(null);\n      setPoc('');\n      setManager('');\n      setTeamLead('');\n      setLaunch('');\n      setWorkday([]);\n      setBillableHours('');\n      setDepartmentId('');\n      setSelectedDepartment(null);\n      setSelectedPoc(null);\n      setSelectedManager(null);\n      setSelectedTeamLead(null);\n\n      // Close modal\n      setVisible(false);\n    } catch (error) {\n      setError(error.message || 'Failed to add team.');\n      console.error('Error adding team:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Convert workday array to Select options for display\n  const workdayOptions = workday.map(day => ({\n    value: day,\n    label: day\n  }));\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-1.5 sm:p-2 bg-white/20 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Users, {\n                className: \"w-4 h-4 sm:w-6 sm:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg sm:text-xl font-semibold\",\n                  children: \"Add New Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white-100 text-xs sm:text-sm\",\n                  children: \"Create a new team for your organization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setVisible(false),\n            className: \"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\",\n        children: [formError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-left font-medium text-red-800 text-sm sm:text-base\",\n              children: \"Action Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 text-xs sm:text-sm\",\n              children: formError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4 sm:space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Building, {\n                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                    children: \"Organization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 sm:space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 36\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select, {\n                      options: departmentOptions,\n                      value: selectedDepartment,\n                      onChange: option => {\n                        setSelectedDepartment(option);\n                        setDepartmentId((option === null || option === void 0 ? void 0 : option.value) || '');\n                      },\n                      placeholder: \"Select Department\",\n                      className: \"w-full\",\n                      isSearchable: true,\n                      isDisabled: loading,\n                      styles: {\n                        control: (base, state) => ({\n                          ...base,\n                          borderRadius: '0.5rem',\n                          borderWidth: '2px',\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                          '&:hover': {\n                            borderColor: '#D1D5DB'\n                          }\n                        })\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Team Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: teamName,\n                      onChange: e => {\n                        setTeamName(e.target.value);\n                        if (error) setError('');\n                      },\n                      onFocus: () => setFocusedField(\"teamName\"),\n                      onBlur: () => setFocusedField(null),\n                      placeholder: \"Enter team name\",\n                      disabled: loading,\n                      className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"teamName\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Upload, {\n                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                    children: \"Team Assets\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 sm:space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Team Logo \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 463,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      onChange: e => setLogo(e.target.files[0]),\n                      accept: \"image/*\",\n                      disabled: loading,\n                      className: \"w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 23\n                    }, this), logo && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: URL.createObjectURL(logo),\n                        alt: \"Logo Preview\",\n                        className: \"w-32 h-16 object-contain rounded-lg border border-gray-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Team Icon \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      onChange: e => setIcon(e.target.files[0]),\n                      accept: \"image/*\",\n                      disabled: loading,\n                      className: \"w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 23\n                    }, this), icon && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: URL.createObjectURL(icon),\n                        alt: \"Icon Preview\",\n                        className: \"w-16 h-16 object-contain rounded-lg border border-gray-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 sm:mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                  children: \"Team Members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Point of Contact \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 40\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    options: pocOptions,\n                    value: selectedPoc,\n                    onChange: option => {\n                      setSelectedPoc(option);\n                      setPoc((option === null || option === void 0 ? void 0 : option.value) || '');\n                    },\n                    placeholder: \"Select POC\",\n                    className: \"w-full\",\n                    isSearchable: true,\n                    isDisabled: loading,\n                    noOptionsMessage: () => \"No options available\",\n                    styles: {\n                      control: (base, state) => ({\n                        ...base,\n                        borderRadius: '0.5rem',\n                        borderWidth: '2px',\n                        borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                        boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                        '&:hover': {\n                          borderColor: '#D1D5DB'\n                        }\n                      })\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Manager \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    options: managerOptions,\n                    value: selectedManager,\n                    onChange: option => {\n                      setSelectedManager(option);\n                      setManager((option === null || option === void 0 ? void 0 : option.value) || '');\n                    },\n                    placeholder: \"Select Manager\",\n                    className: \"w-full\",\n                    isSearchable: true,\n                    isDisabled: loading,\n                    noOptionsMessage: () => \"No managers found\",\n                    styles: {\n                      control: (base, state) => ({\n                        ...base,\n                        borderRadius: '0.5rem',\n                        borderWidth: '2px',\n                        borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                        boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                        '&:hover': {\n                          borderColor: '#D1D5DB'\n                        }\n                      })\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Team Lead \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    options: teamLeadOptions,\n                    value: selectedTeamLead,\n                    onChange: option => {\n                      setSelectedTeamLead(option);\n                      setTeamLead((option === null || option === void 0 ? void 0 : option.value) || '');\n                    },\n                    placeholder: \"Select Team Lead\",\n                    className: \"w-full\",\n                    isSearchable: true,\n                    isDisabled: loading,\n                    noOptionsMessage: () => \"No team leads found\",\n                    styles: {\n                      control: (base, state) => ({\n                        ...base,\n                        borderRadius: '0.5rem',\n                        borderWidth: '2px',\n                        borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                        boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                        '&:hover': {\n                          borderColor: '#D1D5DB'\n                        }\n                      })\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 sm:mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                  children: \"Schedule & Billing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Work Days \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    isMulti: true,\n                    options: daysOfWeek,\n                    value: workdayOptions,\n                    onChange: handleWorkdayChange,\n                    placeholder: \"Select Work Days\",\n                    className: \"w-full\",\n                    isDisabled: loading,\n                    noOptionsMessage: () => \"No options available\",\n                    styles: {\n                      control: (base, state) => ({\n                        ...base,\n                        borderRadius: '0.5rem',\n                        borderWidth: '2px',\n                        borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                        boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                        '&:hover': {\n                          borderColor: '#D1D5DB'\n                        }\n                      })\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 21\n                  }, this), workday.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-2\",\n                    children: [\"Selected: \", workday.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: \"Launch Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    value: launch,\n                    onChange: e => {\n                      setLaunch(e.target.value);\n                      if (error) setError('');\n                    },\n                    onFocus: () => setFocusedField(\"launch\"),\n                    onBlur: () => setFocusedField(null),\n                    disabled: loading,\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"launch\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4 mt-3 sm:mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: \"Billable Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: billableHours,\n                    onChange: e => {\n                      setBillableHours(e.target.value);\n                      if (error) setError('');\n                    },\n                    onFocus: () => setFocusedField(\"billableHours\"),\n                    onBlur: () => setFocusedField(null),\n                    min: \"0\",\n                    placeholder: \"Enter hours\",\n                    disabled: loading,\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"billableHours\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: \"Daily Billable Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: dailyBillableHours,\n                    onChange: e => {\n                      setDailyBillableHours(e.target.value);\n                      if (error) setError('');\n                    },\n                    onFocus: () => setFocusedField(\"dailyBillableHours\"),\n                    onBlur: () => setFocusedField(null),\n                    min: \"0\",\n                    placeholder: \"Enter daily hours\",\n                    disabled: loading,\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"dailyBillableHours\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: \"Weekly Billable Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: weeklyBillableHours,\n                    onChange: e => {\n                      setWeeklyBillableHours(e.target.value);\n                      if (error) setError('');\n                    },\n                    onFocus: () => setFocusedField(\"weeklyBillableHours\"),\n                    onBlur: () => setFocusedField(null),\n                    min: \"0\",\n                    placeholder: \"Enter weekly hours\",\n                    disabled: loading,\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"weeklyBillableHours\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 sm:mt-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-left font-medium text-red-800 text-sm sm:text-base\",\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 text-xs sm:text-sm\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left pt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: `w-56 py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4 ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary hover:bg-secondary text-white'}`,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-rounded animate-spin text-white text-xl font-regular\",\n                  children: \"progress_activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 21\n                }, this), \"Adding...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-rounded text-white text-xl font-regular\",\n                  children: \"add_circle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this), \"Add Team\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n};\n_s(AddTeam, \"9lpprJXPpxWNX6zV5qTOSGAUKVU=\");\n_c = AddTeam;\nexport default AddTeam;\nvar _c;\n$RefreshReg$(_c, \"AddTeam\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "alertMessage", "Select", "X", "Users", "Building", "User", "Calendar", "Clock", "Upload", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "AddTeam", "isVisible", "setVisible", "_s", "users", "setUsers", "departments", "setDepartments", "teams", "setTeams", "teamName", "setTeamName", "icon", "setIcon", "logo", "set<PERSON><PERSON>", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "launch", "setLaunch", "workday", "setWorkday", "billableHours", "setBillableHours", "dailyBillableHours", "setDailyBillableHours", "weeklyBillableHours", "setWeeklyBillableHours", "departmentId", "setDepartmentId", "error", "setError", "errors", "setErrors", "formError", "setFormError", "focusedField", "setFocusedField", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "selectedDepartment", "setSelectedDepartment", "selectedPoc", "setSelectedPoc", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedManager", "selectedTeamLead", "setSelectedTeamLead", "daysOfWeek", "value", "label", "handleWorkdayChange", "selectedOptions", "<PERSON><PERSON><PERSON><PERSON>", "map", "option", "departmentOptions", "dept", "id", "name", "getUserOptions", "allowedRoles", "filter", "user", "_user$resource_types", "hasValidResponsibility", "resource_types", "some", "rt", "includes", "hasValidName", "fullName", "trim", "pocOptions", "managerOptions", "teamLeadOptions", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "processedUsers", "fname", "lname", "roles", "Array", "isArray", "r", "departmentsResponse", "departmentsData", "teamsResponse", "teamsData", "message", "console", "userId", "handleSubmit", "event", "preventDefault", "created<PERSON>y", "trimmedTeamName", "length", "teamExists", "team", "toLowerCase", "setTimeout", "formData", "FormData", "append", "JSON", "stringify", "response", "body", "errorMessage", "status", "statusText", "responseClone", "clone", "errorData", "parseError", "errorText", "text", "textError", "workdayOptions", "day", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "options", "onChange", "placeholder", "isSearchable", "isDisabled", "styles", "control", "base", "state", "borderRadius", "borderWidth", "borderColor", "isFocused", "boxShadow", "type", "e", "target", "onFocus", "onBlur", "disabled", "files", "accept", "src", "URL", "createObjectURL", "alt", "noOptionsMessage", "is<PERSON><PERSON><PERSON>", "join", "min", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team/AddTeam.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\nimport Select from 'react-select';\r\nimport {\r\n  X,\r\n  Users,\r\n  Building,\r\n  User,\r\n  Calendar,\r\n  Clock,\r\n  Upload,\r\n  AlertCircle,\r\n  CheckCircle,\r\n} from \"lucide-react\";\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem('token');\r\n  return token !== null;\r\n};\r\n\r\nconst AddTeam = ({ isVisible, setVisible }) => {\r\n  const [users, setUsers] = useState([]);\r\n  const [departments, setDepartments] = useState([]);\r\n  const [teams, setTeams] = useState([]);\r\n  const [teamName, setTeamName] = useState('');\r\n  const [icon, setIcon] = useState(null);\r\n  const [logo, setLogo] = useState(null);\r\n  const [poc, setPoc] = useState('');\r\n  const [manager, setManager] = useState('');\r\n  const [teamLead, setTeamLead] = useState('');\r\n  const [launch, setLaunch] = useState('');\r\n  const [workday, setWorkday] = useState([]);\r\n  const [billableHours, setBillableHours] = useState('');\r\n  const [dailyBillableHours, setDailyBillableHours] = useState('');\r\n  const [weeklyBillableHours, setWeeklyBillableHours] = useState('');\r\n  const [departmentId, setDepartmentId] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [errors, setErrors] = useState({});\r\n  const [formError, setFormError] = useState('');\r\n  const [focusedField, setFocusedField] = useState(null);\r\n  const [loggedInUser, setLoggedInUser] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // React Select states\r\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\r\n  const [selectedPoc, setSelectedPoc] = useState(null);\r\n  const [selectedManager, setSelectedManager] = useState(null);\r\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\r\n\r\n  // Days of the week for multi-select\r\n  const daysOfWeek = [\r\n    { value: 'Monday', label: 'Monday' },\r\n    { value: 'Tuesday', label: 'Tuesday' },\r\n    { value: 'Wednesday', label: 'Wednesday' },\r\n    { value: 'Thursday', label: 'Thursday' },\r\n    { value: 'Friday', label: 'Friday' },\r\n    { value: 'Saturday', label: 'Saturday' },\r\n    { value: 'Sunday', label: 'Sunday' }\r\n  ];\r\n\r\n  // Handle workday selection\r\n  const handleWorkdayChange = (selectedOptions) => {\r\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\r\n    setWorkday(selectedValues);\r\n  };\r\n\r\n  // Create options for React Select dropdowns\r\n  const departmentOptions = departments.map(dept => ({\r\n    value: dept.id,\r\n    label: dept.name\r\n  }));\r\n\r\n  // Filter users based on responsibility levels\r\n  const getUserOptions = (allowedRoles) => {\r\n    return users\r\n      .filter(user => {\r\n        // Check if user has any of the allowed roles\r\n        const hasValidResponsibility = user.resource_types?.some(rt => \r\n          allowedRoles.includes(rt)\r\n        );\r\n        // Ensure user has a valid name\r\n        const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n        return hasValidResponsibility && hasValidName;\r\n      })\r\n      .map(user => ({\r\n        value: user.fullName,\r\n        label: user.fullName\r\n      }));\r\n  };\r\n\r\n  // Updated filtering based on responsibility level\r\n  const pocOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\r\n  const managerOptions = getUserOptions(['Manager', 'HOD']);\r\n  const teamLeadOptions = getUserOptions(['Team Lead', 'Manager', 'HOD']);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!isVisible || !isTokenValid()) {\r\n        return;\r\n      }\r\n\r\n      const token = localStorage.getItem('token');\r\n\r\n      try {\r\n        setLoading(true);\r\n        setError('');\r\n\r\n        // Fetch Users\r\n        const usersResponse = await fetch(`${API_URL}/users`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!usersResponse.ok) {\r\n          throw new Error('Failed to fetch users');\r\n        }\r\n\r\n        const usersData = await usersResponse.json();\r\n        const processedUsers = usersData.map(user => ({\r\n          id: user.id,\r\n          fullName: `${(user.fname || '').trim()} ${(user.lname || '').trim()}`.trim(),\r\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || '').trim()) : [],\r\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || '').trim()) : [],\r\n        }));\r\n        setUsers(processedUsers);\r\n\r\n        // Fetch Departments\r\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!departmentsResponse.ok) {\r\n          throw new Error('Failed to fetch departments');\r\n        }\r\n\r\n        const departmentsData = await departmentsResponse.json();\r\n        setDepartments(departmentsData.departments || []);\r\n        \r\n        // Fetch Teams for duplicate checking\r\n        const teamsResponse = await fetch(`${API_URL}/teams`, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        if (!teamsResponse.ok) {\r\n          throw new Error('Failed to fetch teams');\r\n        }\r\n\r\n        const teamsData = await teamsResponse.json();\r\n        setTeams(teamsData.teams || []);\r\n      } catch (error) {\r\n        setError(error.message);\r\n        console.error('Error fetching ', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isVisible) {\r\n      fetchData();\r\n    }\r\n  }, [isVisible]);\r\n\r\n  // Fetch logged-in user data (user_id)\r\n  useEffect(() => {\r\n    const userId = localStorage.getItem('user_id');\r\n    if (userId) {\r\n      setLoggedInUser(userId);\r\n    }\r\n  }, []);\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n\r\n    // Get user_id from localStorage for 'created_by'\r\n    const createdBy = loggedInUser;\r\n\r\n    if (!createdBy) {\r\n      setError('User is not logged in.');\r\n      return;\r\n    }\r\n\r\n    const trimmedTeamName = teamName.trim();\r\n\r\n    // Validate required fields\r\n    if (!trimmedTeamName) {\r\n      setError('Team name is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedDepartment?.value) {\r\n      setError('Department is required.');\r\n      return;\r\n    }\r\n\r\n    if (!icon) {\r\n      setError('Icon is required.');\r\n      return;\r\n    }\r\n\r\n    if (!logo) {\r\n      setError('Logo is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedPoc?.value) {\r\n      setError('Point of Contact is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedManager?.value) {\r\n      setError('Manager is required.');\r\n      return;\r\n    }\r\n\r\n    if (!selectedTeamLead?.value) {\r\n      setError('Team Lead is required.');\r\n      return;\r\n    }\r\n\r\n    // Validate workdays\r\n    if (workday.length === 0) {\r\n      setError('At least one workday must be selected.');\r\n      return;\r\n    }\r\n\r\n    // Check if the team already exists (case insensitive)\r\n    const teamExists = teams.some(team => \r\n      team.name.toLowerCase().trim() === trimmedTeamName.toLowerCase()\r\n    );\r\n\r\n    if (teamExists) {\r\n      setError('Team already exists. Please add a different team.');\r\n      setTimeout(() => setError(''), 3000);\r\n      return;\r\n    }\r\n\r\n    setError(''); // Clear any previous error\r\n    setLoading(true);\r\n\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        throw new Error('Authentication token is missing.');\r\n      }\r\n\r\n      const formData = new FormData();\r\n      formData.append('name', trimmedTeamName);\r\n      formData.append('icon', icon);\r\n      formData.append('logo', logo);\r\n      formData.append('poc', selectedPoc?.value || poc);\r\n      formData.append('manager', selectedManager?.value || manager);\r\n      formData.append('team_lead', selectedTeamLead?.value || teamLead);\r\n      formData.append('workday', JSON.stringify(workday));\r\n      formData.append('billable_hours', billableHours || '');\r\n      formData.append('launch', launch || ''); // Allow empty launch date\r\n      formData.append('department_id', selectedDepartment?.value || departmentId);\r\n      formData.append('created_by', createdBy);\r\n\r\n      const response = await fetch(`${API_URL}/teams`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        let errorMessage = `Failed to save team: ${response.status} ${response.statusText}`;\r\n        try {\r\n          // Clone the response to avoid \"body stream already read\" error\r\n          const responseClone = response.clone();\r\n          const errorData = await responseClone.json();\r\n          errorMessage = errorData.error || errorData.message || errorMessage;\r\n        } catch (parseError) {\r\n          // If response is not JSON, get text from original response\r\n          try {\r\n            const errorText = await response.text();\r\n            errorMessage = errorText || 'Server returned an error';\r\n          } catch (textError) {\r\n            console.error('Could not parse error response:', textError);\r\n          }\r\n        }\r\n        throw new Error(errorMessage);\r\n      }\r\n\r\n      await response.json();\r\n      alertMessage('success');\r\n\r\n      // Reset form\r\n      setTeamName('');\r\n      setIcon(null);\r\n      setLogo(null);\r\n      setPoc('');\r\n      setManager('');\r\n      setTeamLead('');\r\n      setLaunch('');\r\n      setWorkday([]);\r\n      setBillableHours('');\r\n      setDepartmentId('');\r\n      setSelectedDepartment(null);\r\n      setSelectedPoc(null);\r\n      setSelectedManager(null);\r\n      setSelectedTeamLead(null);\r\n\r\n      // Close modal\r\n      setVisible(false);\r\n    } catch (error) {\r\n      setError(error.message || 'Failed to add team.');\r\n      console.error('Error adding team:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Convert workday array to Select options for display\r\n  const workdayOptions = workday.map(day => ({\r\n    value: day,\r\n    label: day\r\n  }));\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\">\r\n      <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\">\r\n        {/* Header - Responsive */}\r\n        <div className=\"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2 sm:space-x-3\">\r\n              <div className=\"p-1.5 sm:p-2 bg-white/20 rounded-lg\">\r\n                <Users className=\"w-4 h-4 sm:w-6 sm:h-6\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-left\">\r\n                  <h2 className=\"text-lg sm:text-xl font-semibold\">\r\n                    Add New Team\r\n                  </h2>\r\n                  <p className=\"text-white-100 text-xs sm:text-sm\">\r\n                    Create a new team for your organization\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={() => setVisible(false)}\r\n              className=\"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\"\r\n            >\r\n              <X className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n        {/* Form Content - Responsive */}\r\n        <div className=\"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\">\r\n          {/* Error Message - Responsive */}\r\n          {formError && (\r\n            <div className=\"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\">\r\n              <AlertCircle className=\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\" />\r\n              <div>\r\n                <p className=\"text-left font-medium text-red-800 text-sm sm:text-base\">\r\n                  Action Required\r\n                </p>\r\n                <p className=\"text-red-700 text-xs sm:text-sm\">{formError}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\r\n            {/* Responsive Grid Layout */}\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\r\n              {/* Department & Team Info Section */}\r\n              <div className=\"space-y-3 sm:space-y-4\">\r\n                <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\r\n                  <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\r\n                    <Building className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\r\n                    <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\r\n                      Organization\r\n                    </h3>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3 sm:space-y-4\">\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Department <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <Select\r\n                        options={departmentOptions}\r\n                        value={selectedDepartment}\r\n                        onChange={(option) => {\r\n                          setSelectedDepartment(option);\r\n                          setDepartmentId(option?.value || '');\r\n                        }}\r\n                        placeholder=\"Select Department\"\r\n                        className=\"w-full\"\r\n                        isSearchable\r\n                        isDisabled={loading}\r\n                        styles={{\r\n                          control: (base, state) => ({\r\n                            ...base,\r\n                            borderRadius: '0.5rem',\r\n                            borderWidth: '2px',\r\n                            borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                            boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                            '&:hover': {\r\n                              borderColor: '#D1D5DB'\r\n                            }\r\n                          })\r\n                        }}\r\n                      />\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Team Name <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={teamName}\r\n                        onChange={(e) => {\r\n                          setTeamName(e.target.value);\r\n                          if (error) setError('');\r\n                        }}\r\n                        onFocus={() => setFocusedField(\"teamName\")}\r\n                        onBlur={() => setFocusedField(null)}\r\n                        placeholder=\"Enter team name\"\r\n                        disabled={loading}\r\n                        className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                          focusedField === \"teamName\"\r\n                            ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                            : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                        }`}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Team Assets Section */}\r\n              <div className=\"space-y-3 sm:space-y-4\">\r\n                <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\r\n                  <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\r\n                    <Upload className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\r\n                    <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\r\n                      Team Assets\r\n                    </h3>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3 sm:space-y-4\">\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Team Logo <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <input\r\n                        type=\"file\"\r\n                        onChange={(e) => setLogo(e.target.files[0])}\r\n                        accept=\"image/*\"\r\n                        disabled={loading}\r\n                        className=\"w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      />\r\n                      {logo && (\r\n                        <div className=\"mt-2\">\r\n                          <img\r\n                            src={URL.createObjectURL(logo)}\r\n                            alt=\"Logo Preview\"\r\n                            className=\"w-32 h-16 object-contain rounded-lg border border-gray-200\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Team Icon <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <input\r\n                        type=\"file\"\r\n                        onChange={(e) => setIcon(e.target.files[0])}\r\n                        accept=\"image/*\"\r\n                        disabled={loading}\r\n                        className=\"w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      />\r\n                      {icon && (\r\n                        <div className=\"mt-2\">\r\n                          <img\r\n                            src={URL.createObjectURL(icon)}\r\n                            alt=\"Icon Preview\"\r\n                            className=\"w-16 h-16 object-contain rounded-lg border border-gray-200\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Team Members Section */}\r\n            <div className=\"mt-4 sm:mt-6\">\r\n              <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\r\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\r\n                  <User className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\r\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\r\n                    Team Members\r\n                  </h3>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4\">\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Point of Contact <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <Select\r\n                      options={pocOptions}\r\n                      value={selectedPoc}\r\n                      onChange={(option) => {\r\n                        setSelectedPoc(option);\r\n                        setPoc(option?.value || '');\r\n                      }}\r\n                      placeholder=\"Select POC\"\r\n                      className=\"w-full\"\r\n                      isSearchable\r\n                      isDisabled={loading}\r\n                      noOptionsMessage={() => \"No options available\"}\r\n                      styles={{\r\n                        control: (base, state) => ({\r\n                          ...base,\r\n                          borderRadius: '0.5rem',\r\n                          borderWidth: '2px',\r\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                          '&:hover': {\r\n                            borderColor: '#D1D5DB'\r\n                          }\r\n                        })\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Manager <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <Select\r\n                      options={managerOptions}\r\n                      value={selectedManager}\r\n                      onChange={(option) => {\r\n                        setSelectedManager(option);\r\n                        setManager(option?.value || '');\r\n                      }}\r\n                      placeholder=\"Select Manager\"\r\n                      className=\"w-full\"\r\n                      isSearchable\r\n                      isDisabled={loading}\r\n                      noOptionsMessage={() => \"No managers found\"}\r\n                      styles={{\r\n                        control: (base, state) => ({\r\n                          ...base,\r\n                          borderRadius: '0.5rem',\r\n                          borderWidth: '2px',\r\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                          '&:hover': {\r\n                            borderColor: '#D1D5DB'\r\n                          }\r\n                        })\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Team Lead <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <Select\r\n                      options={teamLeadOptions}\r\n                      value={selectedTeamLead}\r\n                      onChange={(option) => {\r\n                        setSelectedTeamLead(option);\r\n                        setTeamLead(option?.value || '');\r\n                      }}\r\n                      placeholder=\"Select Team Lead\"\r\n                      className=\"w-full\"\r\n                      isSearchable\r\n                      isDisabled={loading}\r\n                      noOptionsMessage={() => \"No team leads found\"}\r\n                      styles={{\r\n                        control: (base, state) => ({\r\n                          ...base,\r\n                          borderRadius: '0.5rem',\r\n                          borderWidth: '2px',\r\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                          '&:hover': {\r\n                            borderColor: '#D1D5DB'\r\n                          }\r\n                        })\r\n                      }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Schedule and Billing Section */}\r\n            <div className=\"mt-4 sm:mt-6\">\r\n              <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\r\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\r\n                  <Calendar className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\r\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\r\n                    Schedule & Billing\r\n                  </h3>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4\">\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Work Days <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <Select\r\n                      isMulti\r\n                      options={daysOfWeek}\r\n                      value={workdayOptions}\r\n                      onChange={handleWorkdayChange}\r\n                      placeholder=\"Select Work Days\"\r\n                      className=\"w-full\"\r\n                      isDisabled={loading}\r\n                      noOptionsMessage={() => \"No options available\"}\r\n                      styles={{\r\n                        control: (base, state) => ({\r\n                          ...base,\r\n                          borderRadius: '0.5rem',\r\n                          borderWidth: '2px',\r\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                          '&:hover': {\r\n                            borderColor: '#D1D5DB'\r\n                          }\r\n                        })\r\n                      }}\r\n                    />\r\n                    {workday.length > 0 && (\r\n                      <p className=\"text-xs text-gray-500 mt-2\">\r\n                        Selected: {workday.join(', ')}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Launch Date\r\n                    </label>\r\n                    <input\r\n                      type=\"date\"\r\n                      value={launch}\r\n                      onChange={(e) => {\r\n                        setLaunch(e.target.value);\r\n                        if (error) setError('');\r\n                      }}\r\n                      onFocus={() => setFocusedField(\"launch\")}\r\n                      onBlur={() => setFocusedField(null)}\r\n                      disabled={loading}\r\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                        focusedField === \"launch\"\r\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      }`}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4 mt-3 sm:mt-4\">\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Billable Hours\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={billableHours}\r\n                      onChange={(e) => {\r\n                        setBillableHours(e.target.value);\r\n                        if (error) setError('');\r\n                      }}\r\n                      onFocus={() => setFocusedField(\"billableHours\")}\r\n                      onBlur={() => setFocusedField(null)}\r\n                      min=\"0\"\r\n                      placeholder=\"Enter hours\"\r\n                      disabled={loading}\r\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                        focusedField === \"billableHours\"\r\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      }`}\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Daily Billable Hours\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={dailyBillableHours}\r\n                      onChange={(e) => {\r\n                        setDailyBillableHours(e.target.value);\r\n                        if (error) setError('');\r\n                      }}\r\n                      onFocus={() => setFocusedField(\"dailyBillableHours\")}\r\n                      onBlur={() => setFocusedField(null)}\r\n                      min=\"0\"\r\n                      placeholder=\"Enter daily hours\"\r\n                      disabled={loading}\r\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                        focusedField === \"dailyBillableHours\"\r\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      }`}\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                      Weekly Billable Hours\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={weeklyBillableHours}\r\n                      onChange={(e) => {\r\n                        setWeeklyBillableHours(e.target.value);\r\n                        if (error) setError('');\r\n                      }}\r\n                      onFocus={() => setFocusedField(\"weeklyBillableHours\")}\r\n                      onBlur={() => setFocusedField(null)}\r\n                      min=\"0\"\r\n                      placeholder=\"Enter weekly hours\"\r\n                      disabled={loading}\r\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                        focusedField === \"weeklyBillableHours\"\r\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                      }`}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Error Display */}\r\n            {error && (\r\n              <div className=\"mt-4 sm:mt-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3\">\r\n                <AlertCircle className=\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\" />\r\n                <div>\r\n                  <p className=\"text-left font-medium text-red-800 text-sm sm:text-base\">\r\n                    Error\r\n                  </p>\r\n                  <p className=\"text-red-700 text-xs sm:text-sm\">{error}</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"text-left pt-6\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className={`w-56 py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4 ${\r\n                  loading \r\n                    ? 'bg-gray-400 cursor-not-allowed' \r\n                    : 'bg-primary hover:bg-secondary text-white'\r\n                }`}\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <span className=\"material-symbols-rounded animate-spin text-white text-xl font-regular\">\r\n                      progress_activity\r\n                    </span>\r\n                    Adding...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <span className=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                    Add Team\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddTeam;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,CAAC,EACDC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,MAAMG,OAAO,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwC,GAAG,EAAEC,MAAM,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACsD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC8D,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM8E,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,eAAe,IAAK;IAC/C,MAAMC,cAAc,GAAGD,eAAe,GAAGA,eAAe,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACN,KAAK,CAAC,GAAG,EAAE;IACzF9B,UAAU,CAACkC,cAAc,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGxD,WAAW,CAACsD,GAAG,CAACG,IAAI,KAAK;IACjDR,KAAK,EAAEQ,IAAI,CAACC,EAAE;IACdR,KAAK,EAAEO,IAAI,CAACE;EACd,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACvC,OAAO/D,KAAK,CACTgE,MAAM,CAACC,IAAI,IAAI;MAAA,IAAAC,oBAAA;MACd;MACA,MAAMC,sBAAsB,IAAAD,oBAAA,GAAGD,IAAI,CAACG,cAAc,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBG,IAAI,CAACC,EAAE,IACzDP,YAAY,CAACQ,QAAQ,CAACD,EAAE,CAC1B,CAAC;MACD;MACA,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;MACjE,OAAOP,sBAAsB,IAAIK,YAAY;IAC/C,CAAC,CAAC,CACDhB,GAAG,CAACS,IAAI,KAAK;MACZd,KAAK,EAAEc,IAAI,CAACQ,QAAQ;MACpBrB,KAAK,EAAEa,IAAI,CAACQ;IACd,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAME,UAAU,GAAGb,cAAc,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAClE,MAAMc,cAAc,GAAGd,cAAc,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;EACzD,MAAMe,eAAe,GAAGf,cAAc,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAEvE3F,SAAS,CAAC,MAAM;IACd,MAAM2G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACjF,SAAS,IAAI,CAACL,YAAY,CAAC,CAAC,EAAE;QACjC;MACF;MAEA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI;QACF8C,UAAU,CAAC,IAAI,CAAC;QAChBV,QAAQ,CAAC,EAAE,CAAC;;QAEZ;QACA,MAAMgD,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAG5F,OAAO,QAAQ,EAAE;UACpD6F,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzF,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACsF,aAAa,CAACI,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5C,MAAMC,cAAc,GAAGF,SAAS,CAAC7B,GAAG,CAACS,IAAI,KAAK;UAC5CL,EAAE,EAAEK,IAAI,CAACL,EAAE;UACXa,QAAQ,EAAE,GAAG,CAACR,IAAI,CAACuB,KAAK,IAAI,EAAE,EAAEd,IAAI,CAAC,CAAC,IAAI,CAACT,IAAI,CAACwB,KAAK,IAAI,EAAE,EAAEf,IAAI,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;UAC5EgB,KAAK,EAAEC,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAACyB,KAAK,CAAC,GAAGzB,IAAI,CAACyB,KAAK,CAAClC,GAAG,CAACqC,CAAC,IAAI,CAACA,CAAC,CAAChC,IAAI,IAAI,EAAE,EAAEa,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;UAClFN,cAAc,EAAEuB,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAACG,cAAc,CAAC,GAAGH,IAAI,CAACG,cAAc,CAACZ,GAAG,CAACc,EAAE,IAAI,CAACA,EAAE,CAACT,IAAI,IAAI,EAAE,EAAEa,IAAI,CAAC,CAAC,CAAC,GAAG;QAC/G,CAAC,CAAC,CAAC;QACHzE,QAAQ,CAACsF,cAAc,CAAC;;QAExB;QACA,MAAMO,mBAAmB,GAAG,MAAMd,KAAK,CAAC,GAAG5F,OAAO,cAAc,EAAE;UAChE6F,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzF,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACqG,mBAAmB,CAACX,EAAE,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,MAAMW,eAAe,GAAG,MAAMD,mBAAmB,CAACR,IAAI,CAAC,CAAC;QACxDnF,cAAc,CAAC4F,eAAe,CAAC7F,WAAW,IAAI,EAAE,CAAC;;QAEjD;QACA,MAAM8F,aAAa,GAAG,MAAMhB,KAAK,CAAC,GAAG5F,OAAO,QAAQ,EAAE;UACpD6F,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzF,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACuG,aAAa,CAACb,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMa,SAAS,GAAG,MAAMD,aAAa,CAACV,IAAI,CAAC,CAAC;QAC5CjF,QAAQ,CAAC4F,SAAS,CAAC7F,KAAK,IAAI,EAAE,CAAC;MACjC,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACoE,OAAO,CAAC;QACvBC,OAAO,CAACrE,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACzC,CAAC,SAAS;QACRW,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAI5C,SAAS,EAAE;MACbiF,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACjF,SAAS,CAAC,CAAC;;EAEf;EACA1B,SAAS,CAAC,MAAM;IACd,MAAMiI,MAAM,GAAG1G,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAIyG,MAAM,EAAE;MACV7D,eAAe,CAAC6D,MAAM,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAOC,KAAK,IAAK;IACpCA,KAAK,CAACC,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAS,GAAGlE,YAAY;IAE9B,IAAI,CAACkE,SAAS,EAAE;MACdzE,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,MAAM0E,eAAe,GAAGnG,QAAQ,CAACoE,IAAI,CAAC,CAAC;;IAEvC;IACA,IAAI,CAAC+B,eAAe,EAAE;MACpB1E,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAI,EAACW,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAES,KAAK,GAAE;MAC9BpB,QAAQ,CAAC,yBAAyB,CAAC;MACnC;IACF;IAEA,IAAI,CAACvB,IAAI,EAAE;MACTuB,QAAQ,CAAC,mBAAmB,CAAC;MAC7B;IACF;IAEA,IAAI,CAACrB,IAAI,EAAE;MACTqB,QAAQ,CAAC,mBAAmB,CAAC;MAC7B;IACF;IAEA,IAAI,EAACa,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEO,KAAK,GAAE;MACvBpB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI,EAACe,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEK,KAAK,GAAE;MAC3BpB,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,IAAI,EAACiB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEG,KAAK,GAAE;MAC5BpB,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;;IAEA;IACA,IAAIX,OAAO,CAACsF,MAAM,KAAK,CAAC,EAAE;MACxB3E,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;;IAEA;IACA,MAAM4E,UAAU,GAAGvG,KAAK,CAACiE,IAAI,CAACuC,IAAI,IAChCA,IAAI,CAAC/C,IAAI,CAACgD,WAAW,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,KAAK+B,eAAe,CAACI,WAAW,CAAC,CACjE,CAAC;IAED,IAAIF,UAAU,EAAE;MACd5E,QAAQ,CAAC,mDAAmD,CAAC;MAC7D+E,UAAU,CAAC,MAAM/E,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACpC;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdU,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMhD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAI2F,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAER,eAAe,CAAC;MACxCM,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEzG,IAAI,CAAC;MAC7BuG,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvG,IAAI,CAAC;MAC7BqG,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAE,CAAArE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,KAAK,KAAIvC,GAAG,CAAC;MACjDmG,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE,CAAAnE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,KAAK,KAAIrC,OAAO,CAAC;MAC7DiG,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE,CAAAjE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEG,KAAK,KAAInC,QAAQ,CAAC;MACjE+F,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAAC/F,OAAO,CAAC,CAAC;MACnD2F,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE3F,aAAa,IAAI,EAAE,CAAC;MACtDyF,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/F,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;MACzC6F,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE,CAAAvE,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAES,KAAK,KAAIvB,YAAY,CAAC;MAC3EmF,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAET,SAAS,CAAC;MAExC,MAAMY,QAAQ,GAAG,MAAMpC,KAAK,CAAC,GAAG5F,OAAO,QAAQ,EAAE;QAC/C6F,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUzF,KAAK;QAClC,CAAC;QACD4H,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACK,QAAQ,CAACjC,EAAE,EAAE;QAChB,IAAImC,YAAY,GAAG,wBAAwBF,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACI,UAAU,EAAE;QACnF,IAAI;UACF;UACA,MAAMC,aAAa,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC;UACtC,MAAMC,SAAS,GAAG,MAAMF,aAAa,CAACnC,IAAI,CAAC,CAAC;UAC5CgC,YAAY,GAAGK,SAAS,CAAC7F,KAAK,IAAI6F,SAAS,CAACzB,OAAO,IAAIoB,YAAY;QACrE,CAAC,CAAC,OAAOM,UAAU,EAAE;UACnB;UACA,IAAI;YACF,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;YACvCR,YAAY,GAAGO,SAAS,IAAI,0BAA0B;UACxD,CAAC,CAAC,OAAOE,SAAS,EAAE;YAClB5B,OAAO,CAACrE,KAAK,CAAC,iCAAiC,EAAEiG,SAAS,CAAC;UAC7D;QACF;QACA,MAAM,IAAI3C,KAAK,CAACkC,YAAY,CAAC;MAC/B;MAEA,MAAMF,QAAQ,CAAC9B,IAAI,CAAC,CAAC;MACrBjH,YAAY,CAAC,SAAS,CAAC;;MAEvB;MACAkC,WAAW,CAAC,EAAE,CAAC;MACfE,OAAO,CAAC,IAAI,CAAC;MACbE,OAAO,CAAC,IAAI,CAAC;MACbE,MAAM,CAAC,EAAE,CAAC;MACVE,UAAU,CAAC,EAAE,CAAC;MACdE,WAAW,CAAC,EAAE,CAAC;MACfE,SAAS,CAAC,EAAE,CAAC;MACbE,UAAU,CAAC,EAAE,CAAC;MACdE,gBAAgB,CAAC,EAAE,CAAC;MACpBM,eAAe,CAAC,EAAE,CAAC;MACnBc,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,IAAI,CAAC;MACxBE,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAnD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACoE,OAAO,IAAI,qBAAqB,CAAC;MAChDC,OAAO,CAACrE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC,SAAS;MACRW,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuF,cAAc,GAAG5G,OAAO,CAACoC,GAAG,CAACyE,GAAG,KAAK;IACzC9E,KAAK,EAAE8E,GAAG;IACV7E,KAAK,EAAE6E;EACT,CAAC,CAAC,CAAC;EAEH,IAAI,CAACpI,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEZ,OAAA;IAAKiJ,SAAS,EAAC,6HAA6H;IAAAC,QAAA,eAC1IlJ,OAAA;MAAKiJ,SAAS,EAAC,iMAAiM;MAAAC,QAAA,gBAE9MlJ,OAAA;QAAKiJ,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DlJ,OAAA;UAAKiJ,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlJ,OAAA;YAAKiJ,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDlJ,OAAA;cAAKiJ,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClDlJ,OAAA,CAACT,KAAK;gBAAC0J,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNtJ,OAAA;cAAAkJ,QAAA,eACElJ,OAAA;gBAAKiJ,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlJ,OAAA;kBAAIiJ,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtJ,OAAA;kBAAGiJ,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtJ,OAAA;YACEuJ,OAAO,EAAEA,CAAA,KAAM1I,UAAU,CAAC,KAAK,CAAE;YACjCoI,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpFlJ,OAAA,CAACV,CAAC;cAAC2J,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtJ,OAAA;QAAKiJ,SAAS,EAAC,iFAAiF;QAAAC,QAAA,GAE7FjG,SAAS,iBACRjD,OAAA;UAAKiJ,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,gBACtKlJ,OAAA,CAACH,WAAW;YAACoJ,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFtJ,OAAA;YAAAkJ,QAAA,gBACElJ,OAAA;cAAGiJ,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtJ,OAAA;cAAGiJ,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEjG;YAAS;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDtJ,OAAA;UAAMwJ,QAAQ,EAAEpC,YAAa;UAAC6B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAE9DlJ,OAAA;YAAKiJ,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAE7DlJ,OAAA;cAAKiJ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrClJ,OAAA;gBAAKiJ,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACpFlJ,OAAA;kBAAKiJ,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,gBACvDlJ,OAAA,CAACR,QAAQ;oBAACyJ,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5DtJ,OAAA;oBAAIiJ,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAENtJ,OAAA;kBAAKiJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClJ,OAAA;oBAAAkJ,QAAA,gBACElJ,OAAA;sBAAOiJ,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,aAClF,eAAAlJ,OAAA;wBAAMiJ,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACRtJ,OAAA,CAACX,MAAM;sBACLoK,OAAO,EAAEhF,iBAAkB;sBAC3BP,KAAK,EAAET,kBAAmB;sBAC1BiG,QAAQ,EAAGlF,MAAM,IAAK;wBACpBd,qBAAqB,CAACc,MAAM,CAAC;wBAC7B5B,eAAe,CAAC,CAAA4B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;sBACtC,CAAE;sBACFyF,WAAW,EAAC,mBAAmB;sBAC/BV,SAAS,EAAC,QAAQ;sBAClBW,YAAY;sBACZC,UAAU,EAAEtG,OAAQ;sBACpBuG,MAAM,EAAE;wBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;0BACzB,GAAGD,IAAI;0BACPE,YAAY,EAAE,QAAQ;0BACtBC,WAAW,EAAE,KAAK;0BAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;0BACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;0BACvE,SAAS,EAAE;4BACTD,WAAW,EAAE;0BACf;wBACF,CAAC;sBACH;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENtJ,OAAA;oBAAAkJ,QAAA,gBACElJ,OAAA;sBAAOiJ,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,YACnF,eAAAlJ,OAAA;wBAAMiJ,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACRtJ,OAAA;sBACEuK,IAAI,EAAC,MAAM;sBACXrG,KAAK,EAAE7C,QAAS;sBAChBqI,QAAQ,EAAGc,CAAC,IAAK;wBACflJ,WAAW,CAACkJ,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC;wBAC3B,IAAIrB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;sBACzB,CAAE;sBACF4H,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,UAAU,CAAE;sBAC3CuH,MAAM,EAAEA,CAAA,KAAMvH,eAAe,CAAC,IAAI,CAAE;sBACpCuG,WAAW,EAAC,iBAAiB;sBAC7BiB,QAAQ,EAAErH,OAAQ;sBAClB0F,SAAS,EAAE,8HACT9F,YAAY,KAAK,UAAU,GACvB,oDAAoD,GACpD,4DAA4D;oBAC/D;sBAAAgG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtJ,OAAA;cAAKiJ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrClJ,OAAA;gBAAKiJ,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACpFlJ,OAAA;kBAAKiJ,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,gBACvDlJ,OAAA,CAACJ,MAAM;oBAACqJ,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1DtJ,OAAA;oBAAIiJ,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAENtJ,OAAA;kBAAKiJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClJ,OAAA;oBAAAkJ,QAAA,gBACElJ,OAAA;sBAAOiJ,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,YACnF,eAAAlJ,OAAA;wBAAMiJ,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACRtJ,OAAA;sBACEuK,IAAI,EAAC,MAAM;sBACXb,QAAQ,EAAGc,CAAC,IAAK9I,OAAO,CAAC8I,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE;sBAC5CC,MAAM,EAAC,SAAS;sBAChBF,QAAQ,EAAErH,OAAQ;sBAClB0F,SAAS,EAAC;oBAAuL;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClM,CAAC,EACD7H,IAAI,iBACHzB,OAAA;sBAAKiJ,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnBlJ,OAAA;wBACE+K,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACxJ,IAAI,CAAE;wBAC/ByJ,GAAG,EAAC,cAAc;wBAClBjC,SAAS,EAAC;sBAA4D;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENtJ,OAAA;oBAAAkJ,QAAA,gBACElJ,OAAA;sBAAOiJ,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,YACnF,eAAAlJ,OAAA;wBAAMiJ,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACRtJ,OAAA;sBACEuK,IAAI,EAAC,MAAM;sBACXb,QAAQ,EAAGc,CAAC,IAAKhJ,OAAO,CAACgJ,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE;sBAC5CC,MAAM,EAAC,SAAS;sBAChBF,QAAQ,EAAErH,OAAQ;sBAClB0F,SAAS,EAAC;oBAAuL;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClM,CAAC,EACD/H,IAAI,iBACHvB,OAAA;sBAAKiJ,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnBlJ,OAAA;wBACE+K,GAAG,EAAEC,GAAG,CAACC,eAAe,CAAC1J,IAAI,CAAE;wBAC/B2J,GAAG,EAAC,cAAc;wBAClBjC,SAAS,EAAC;sBAA4D;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtJ,OAAA;YAAKiJ,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BlJ,OAAA;cAAKiJ,SAAS,EAAC,uEAAuE;cAAAC,QAAA,gBACpFlJ,OAAA;gBAAKiJ,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvDlJ,OAAA,CAACP,IAAI;kBAACwJ,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDtJ,OAAA;kBAAIiJ,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE/D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENtJ,OAAA;gBAAKiJ,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,gBAC7DlJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,mBAC5E,eAAAlJ,OAAA;sBAAMiJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACRtJ,OAAA,CAACX,MAAM;oBACLoK,OAAO,EAAE/D,UAAW;oBACpBxB,KAAK,EAAEP,WAAY;oBACnB+F,QAAQ,EAAGlF,MAAM,IAAK;sBACpBZ,cAAc,CAACY,MAAM,CAAC;sBACtB5C,MAAM,CAAC,CAAA4C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;oBAC7B,CAAE;oBACFyF,WAAW,EAAC,YAAY;oBACxBV,SAAS,EAAC,QAAQ;oBAClBW,YAAY;oBACZC,UAAU,EAAEtG,OAAQ;oBACpB4H,gBAAgB,EAAEA,CAAA,KAAM,sBAAuB;oBAC/CrB,MAAM,EAAE;sBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;wBACzB,GAAGD,IAAI;wBACPE,YAAY,EAAE,QAAQ;wBACtBC,WAAW,EAAE,KAAK;wBAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;wBACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;wBACvE,SAAS,EAAE;0BACTD,WAAW,EAAE;wBACf;sBACF,CAAC;oBACH;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,UACrF,eAAAlJ,OAAA;sBAAMiJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACRtJ,OAAA,CAACX,MAAM;oBACLoK,OAAO,EAAE9D,cAAe;oBACxBzB,KAAK,EAAEL,eAAgB;oBACvB6F,QAAQ,EAAGlF,MAAM,IAAK;sBACpBV,kBAAkB,CAACU,MAAM,CAAC;sBAC1B1C,UAAU,CAAC,CAAA0C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;oBACjC,CAAE;oBACFyF,WAAW,EAAC,gBAAgB;oBAC5BV,SAAS,EAAC,QAAQ;oBAClBW,YAAY;oBACZC,UAAU,EAAEtG,OAAQ;oBACpB4H,gBAAgB,EAAEA,CAAA,KAAM,mBAAoB;oBAC5CrB,MAAM,EAAE;sBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;wBACzB,GAAGD,IAAI;wBACPE,YAAY,EAAE,QAAQ;wBACtBC,WAAW,EAAE,KAAK;wBAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;wBACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;wBACvE,SAAS,EAAE;0BACTD,WAAW,EAAE;wBACf;sBACF,CAAC;oBACH;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,YACnF,eAAAlJ,OAAA;sBAAMiJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACRtJ,OAAA,CAACX,MAAM;oBACLoK,OAAO,EAAE7D,eAAgB;oBACzB1B,KAAK,EAAEH,gBAAiB;oBACxB2F,QAAQ,EAAGlF,MAAM,IAAK;sBACpBR,mBAAmB,CAACQ,MAAM,CAAC;sBAC3BxC,WAAW,CAAC,CAAAwC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEN,KAAK,KAAI,EAAE,CAAC;oBAClC,CAAE;oBACFyF,WAAW,EAAC,kBAAkB;oBAC9BV,SAAS,EAAC,QAAQ;oBAClBW,YAAY;oBACZC,UAAU,EAAEtG,OAAQ;oBACpB4H,gBAAgB,EAAEA,CAAA,KAAM,qBAAsB;oBAC9CrB,MAAM,EAAE;sBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;wBACzB,GAAGD,IAAI;wBACPE,YAAY,EAAE,QAAQ;wBACtBC,WAAW,EAAE,KAAK;wBAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;wBACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;wBACvE,SAAS,EAAE;0BACTD,WAAW,EAAE;wBACf;sBACF,CAAC;oBACH;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtJ,OAAA;YAAKiJ,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BlJ,OAAA;cAAKiJ,SAAS,EAAC,uEAAuE;cAAAC,QAAA,gBACpFlJ,OAAA;gBAAKiJ,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvDlJ,OAAA,CAACN,QAAQ;kBAACuJ,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DtJ,OAAA;kBAAIiJ,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE/D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENtJ,OAAA;gBAAKiJ,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,gBAC7DlJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,YACnF,eAAAlJ,OAAA;sBAAMiJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACRtJ,OAAA,CAACX,MAAM;oBACL+L,OAAO;oBACP3B,OAAO,EAAExF,UAAW;oBACpBC,KAAK,EAAE6E,cAAe;oBACtBW,QAAQ,EAAEtF,mBAAoB;oBAC9BuF,WAAW,EAAC,kBAAkB;oBAC9BV,SAAS,EAAC,QAAQ;oBAClBY,UAAU,EAAEtG,OAAQ;oBACpB4H,gBAAgB,EAAEA,CAAA,KAAM,sBAAuB;oBAC/CrB,MAAM,EAAE;sBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;wBACzB,GAAGD,IAAI;wBACPE,YAAY,EAAE,QAAQ;wBACtBC,WAAW,EAAE,KAAK;wBAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;wBACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;wBACvE,SAAS,EAAE;0BACTD,WAAW,EAAE;wBACf;sBACF,CAAC;oBACH;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACDnH,OAAO,CAACsF,MAAM,GAAG,CAAC,iBACjBzH,OAAA;oBAAGiJ,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,YAC9B,EAAC/G,OAAO,CAACkJ,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,EAAC;kBAE/F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtJ,OAAA;oBACEuK,IAAI,EAAC,MAAM;oBACXrG,KAAK,EAAEjC,MAAO;oBACdyH,QAAQ,EAAGc,CAAC,IAAK;sBACftI,SAAS,CAACsI,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC;sBACzB,IAAIrB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;oBACzB,CAAE;oBACF4H,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,QAAQ,CAAE;oBACzCuH,MAAM,EAAEA,CAAA,KAAMvH,eAAe,CAAC,IAAI,CAAE;oBACpCwH,QAAQ,EAAErH,OAAQ;oBAClB0F,SAAS,EAAE,8HACT9F,YAAY,KAAK,QAAQ,GACrB,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtJ,OAAA;gBAAKiJ,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1ElJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,EAAC;kBAE/F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtJ,OAAA;oBACEuK,IAAI,EAAC,QAAQ;oBACbrG,KAAK,EAAE7B,aAAc;oBACrBqH,QAAQ,EAAGc,CAAC,IAAK;sBACflI,gBAAgB,CAACkI,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC;sBAChC,IAAIrB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;oBACzB,CAAE;oBACF4H,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,eAAe,CAAE;oBAChDuH,MAAM,EAAEA,CAAA,KAAMvH,eAAe,CAAC,IAAI,CAAE;oBACpCkI,GAAG,EAAC,GAAG;oBACP3B,WAAW,EAAC,aAAa;oBACzBiB,QAAQ,EAAErH,OAAQ;oBAClB0F,SAAS,EAAE,8HACT9F,YAAY,KAAK,eAAe,GAC5B,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,EAAC;kBAE/F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtJ,OAAA;oBACEuK,IAAI,EAAC,QAAQ;oBACbrG,KAAK,EAAE3B,kBAAmB;oBAC1BmH,QAAQ,EAAGc,CAAC,IAAK;sBACfhI,qBAAqB,CAACgI,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC;sBACrC,IAAIrB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;oBACzB,CAAE;oBACF4H,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,oBAAoB,CAAE;oBACrDuH,MAAM,EAAEA,CAAA,KAAMvH,eAAe,CAAC,IAAI,CAAE;oBACpCkI,GAAG,EAAC,GAAG;oBACP3B,WAAW,EAAC,mBAAmB;oBAC/BiB,QAAQ,EAAErH,OAAQ;oBAClB0F,SAAS,EAAE,8HACT9F,YAAY,KAAK,oBAAoB,GACjC,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAOiJ,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,EAAC;kBAE/F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtJ,OAAA;oBACEuK,IAAI,EAAC,QAAQ;oBACbrG,KAAK,EAAEzB,mBAAoB;oBAC3BiH,QAAQ,EAAGc,CAAC,IAAK;sBACf9H,sBAAsB,CAAC8H,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC;sBACtC,IAAIrB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;oBACzB,CAAE;oBACF4H,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,qBAAqB,CAAE;oBACtDuH,MAAM,EAAEA,CAAA,KAAMvH,eAAe,CAAC,IAAI,CAAE;oBACpCkI,GAAG,EAAC,GAAG;oBACP3B,WAAW,EAAC,oBAAoB;oBAChCiB,QAAQ,EAAErH,OAAQ;oBAClB0F,SAAS,EAAE,8HACT9F,YAAY,KAAK,qBAAqB,GAClC,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLzG,KAAK,iBACJ7C,OAAA;YAAKiJ,SAAS,EAAC,0HAA0H;YAAAC,QAAA,gBACvIlJ,OAAA,CAACH,WAAW;cAACoJ,SAAS,EAAC;YAAyD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnFtJ,OAAA;cAAAkJ,QAAA,gBACElJ,OAAA;gBAAGiJ,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EAAC;cAEvE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtJ,OAAA;gBAAGiJ,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAErG;cAAK;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDtJ,OAAA;YAAKiJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BlJ,OAAA;cACEuK,IAAI,EAAC,QAAQ;cACbK,QAAQ,EAAErH,OAAQ;cAClB0F,SAAS,EAAE,sFACT1F,OAAO,GACH,gCAAgC,GAChC,0CAA0C,EAC7C;cAAA2F,QAAA,EAEF3F,OAAO,gBACNvD,OAAA,CAAAE,SAAA;gBAAAgJ,QAAA,gBACElJ,OAAA;kBAAMiJ,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,EAAC;gBAExF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,aAET;cAAA,eAAE,CAAC,gBAEHtJ,OAAA,CAAAE,SAAA;gBAAAgJ,QAAA,gBACElJ,OAAA;kBAAMiJ,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,YAE9F;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxI,EAAA,CA3wBIH,OAAO;AAAA4K,EAAA,GAAP5K,OAAO;AA6wBb,eAAeA,OAAO;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}