import React, { useState, useCallback, useEffect, useMemo } from "react";
// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";
// Loading spinner component to show while data is loading
import Loading from "./../../common/Loading";
import { confirmationAlert, ManageColumns, SearchFilter, TableView, Image } from './../../common/coreui';
import { useDispatch } from "react-redux";
import { defaultDateFormat, defaultDateTimeFormat, removeKeys, sortByLabel, secondsToHours } from "./../../utils";
// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import { userDataApi, useDeleteUserDataMutation, useGetUserDataQuery, useLazyFetchDataOptionsForUserDataQuery } from "./../../features/api/userDataApi";
import { useNavigate } from "react-router-dom";
import { DateTimeFormatDay, DateTimeFormatHour, DateTimeFormatTable } from "./../../common/DateTimeFormatTable";
import { useRoleBasedAccess } from "../../common/useRoleBasedAccess";

// API endpoint and configuration constants
const MODULE_NAME = "Team Snapshot";

// Define the list of columns that should be visible
const VISIBLE_COLUMNS = [
  "Action",
  "S.No",
  "EID",
  "Full Name",
  "Designation",
  "Email",
  "Responsibility Label",
  "Department", // Maps to 'departments'
  "Team", // Maps to 'teams'
  "Team Lead", // Maps to 'team_lead'
  "Desk ID", // Maps to 'desk_id'
  "Billing Status", // Maps to 'billing_statuses'
  "Resource Status", // Maps to 'resource_statuses'
  "Contract Type", // Maps to 'contact_types'
  "Available Status", // Maps to 'available_statuses'
  "Work Location", // Maps to 'locations'
  "Office Branch", // Maps to 'branches'
  "On-Site Status", // Maps to 'onsite_statuses'
  "Team Member Status", // Maps to 'member_statuses'
  "Billable Hours", // Maps to 'billable_hours'
  "Daily Billable Hours", // Maps to 'daily_billable_hours'
  "Weekly Billable Hours", // Maps to 'weekly_billable_hours'
];

// Main component for listing Team Snapshot Data
const TeamSnapshotDataList = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [dataItemsId, setDataItemsId] = useState(null);
  const [error, setError] = useState(null);
  const [viewData, setViewData] = useState(null);
  const navigate = useNavigate();
  const { rolePermissions } = useRoleBasedAccess();
  const summaryReportBtn = "w-auto whitespace-nowrap m-1 h-[40px] py-2 px-2 text-sm font-medium text-gray-900 bg-green-100 rounded-lg border border-green-500 hover:bg-green-300 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 capitalize";

  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);

  const { data: dataItems, isFetching, error: fetchError } = useGetUserDataQuery({
    sort_by: sortColumn,
    order: sortDirection,
    page: currentPage,
    per_page: perPage,
    query: queryString,
  });

  const [triggerFilterByFetch] = useLazyFetchDataOptionsForUserDataQuery();
  const [deleteUserData] = useDeleteUserDataMutation();

  // --- State for Summary Data ---
  const [summaryData, setSummaryData] = useState({
    teamLead: "N/A",
    liveMembers: 0,
    benchMembers: 0,
    totalDesigners: 0,
    totalDevelopers: 0,
    totalQA: 0,
  });

  // --- Function to Calculate Summary Data ---
  const calculateSummaryData = useCallback((items) => {
    if (!items || items.length === 0) {
      setSummaryData({
        teamLead: "N/A",
        liveMembers: 0,
        benchMembers: 0,
        totalDesigners: 0,
        totalDevelopers: 0,
        totalQA: 0,
      });
      return;
    }

    let liveCount = 0;
    let benchCount = 0;
    let designerCount = 0;
    let developerCount = 0;
    let qaCount = 0;
    let leadName = "N/A";

    // Find Team Lead (example logic - adjust based on your data structure)
    const leadUser = items.find(item =>
      item.designations &&
      item.designations.some(designation =>
        designation.name.toLowerCase().includes("lead") ||
        designation.name.toLowerCase().includes("manager")
      )
    );

    if (leadUser) {
      leadName = `${leadUser.fname || ''} ${leadUser.lname || ''}`.trim() || leadUser.fname || "N/A";
    }

    items.forEach(item => {
      // Member Status Counts
      const statusNames = item.resource_statuses?.map(s => s.name.toLowerCase()) || [];
      if (statusNames.includes('live') || statusNames.includes('active')) {
        liveCount++;
      } else if (statusNames.includes('bench')) {
        benchCount++;
      }

      // Role Counts
      const designationNames = item.designations?.map(d => d.name.toLowerCase()) || [];
      if (designationNames.some(name => name.includes('designer'))) {
        designerCount++;
      }
      if (designationNames.some(name => name.includes('developer'))) {
        developerCount++;
      }
      if (designationNames.some(name => name.includes('qa') || name.includes('quality'))) {
        qaCount++;
      }
    });

    setSummaryData({
      teamLead: leadName,
      liveMembers: liveCount,
      benchMembers: benchCount,
      totalDesigners: designerCount,
      totalDevelopers: developerCount,
      totalQA: qaCount,
    });
  }, []);

 
  useEffect(() => {
    if (dataItems?.data) {
      calculateSummaryData(dataItems.data);
    } else {
      calculateSummaryData([]);
    }
  }, [dataItems, calculateSummaryData]);

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "");
    setQueryString(q);
  };

  const handleEdit = (id) => {
    setViewData(null);
    setDataItemsId(id);
    setModalVisible(true);
  };

  const handleDelete = (id) => {
    confirmationAlert({
      onConfirm: () => {
        deleteUserData(id);
        setViewData(null);
      },
    });
  };

  let columnSerial = 1;
  const allColumns = useMemo(() => [
    {
      id: columnSerial++,
      name: "Action",
      width: "100px",
      cell: (item) => (
        <div className="flex justify-center">
          <button
            className="flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            onClick={() => {
              setViewData(item);
            }}
            title="View"
          >
            <span className="material-symbols-outlined text-lg">visibility</span>
          </button>
        </div>
      ),
    },
    {
      id: columnSerial++,
      name: "S.No",
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },
    {
      id: columnSerial++,
      name: "EID",
      db_field: "eid",
      selector: (row) => row.eid || "",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Full Name",
      width: '300px',
      db_field: "id",
      selector: (row) => row?.fname ? `${row?.fname} ${row?.lname}` : "",
      cell: (row) => (
        <span className="flex align-middle items-center w-full text-start gap-2 px-4 py-2">
          {row?.photo ? (
            <Image
              src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row?.photo}`}
              alt="Profile Photo"
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <span className="min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full">No <br />Photo</span>
          )}
          <div className="flex flex-col min-w-[80%]">
            {row?.fname && <b>{row.fname} {row.lname}</b>}
            {row?.designations?.length > 0 ? (
              <div>{row.designations.map(designation => designation.name).join(', ')}</div>
            ) : (
              <div className="text-sm text-gray-400">N/A</div>
            )}
          </div>
        </span>
      ),
      sortable: true,
      omit: false,
      filterable: false,
    },
    {
      id: columnSerial++,
      name: "Email",
      db_field: "email",
      selector: (row) => row.email || "",
      cell: (row) => <span className="lowercase">{row.email || ""}</span>,
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Responsibility Label",
      db_field: "resource_types",
      selector: (row) => row.resource_types?.map(type => type.name).join(', ') || "N/A",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'resource_types',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Department",
      db_field: "departments",
      selector: (row) => row.departments?.map(department => department.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'departments',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Team",
      db_field: "teams",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'teams',
      omit: false,
      sortable: true,
      filterable: true,
      className: 'team-column',
      cell: (row) => (
        <div className="team-cell-index">
          {row.team_names?.join(', ') || "N/A"}
        </div>
      ),
    },
    {
      id: columnSerial++,
      name: "Team Lead",
      db_field: "team_lead",
      selector: (row) => {
        const teamLead = row.teams && row.teams[0] ? row.teams[0].team_lead : null;
        return teamLead || "N/A";
      },
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Desk ID",
      db_field: "desk_id",
      selector: (row) => row.desk_id || "N/A",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Billing Status",
      db_field: "billing_statuses",
      selector: (row) => row.billing_statuses?.map(status => status.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'billing_statuses',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Resource Status",
      db_field: "resource_statuses",
      selector: (row) => row.resource_statuses?.map(status => status.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'resource_statuses',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Contract Type",
      db_field: "contact_types",
      selector: (row) => row.contact_types?.map(contact => contact.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'contact_types',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Available Status",
      db_field: "available_statuses",
      selector: (row) => row.available_statuses?.map(status => status.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'available_statuses',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Work Location",
      db_field: "locations",
      selector: (row) => {
        const locations = row?.branches?.flatMap(branch =>
          branch.locations?.map(location => location.locations_name)
        ) || [];
        return locations.join(', ') || "";
      },
      selectorForFilter: (row) => row.locations_name || "N/A",
      tableNameForFilter: 'locations',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Office Branch",
      db_field: "branches",
      selector: (row) => row.branches?.map(branch => branch.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'branches',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "On-Site Status",
      db_field: "onsite_statuses",
      selector: (row) => row.onsite_statuses?.map(status => status.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'onsite_statuses',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Team Member Status",
      db_field: "member_statuses",
      selector: (row) => row.member_statuses?.map(status => status.name).join(', ') || "",
      selectorForFilter: (row) => row.name || "N/A",
      tableNameForFilter: 'member_statuses',
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Billable Hours",
      db_field: "billable_hours",
      selector: (row) => row.billable_hours || "",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Daily Billable Hours",
      db_field: "daily_billable_hours",
      selector: (row) => row.daily_billable_hours || "",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Weekly Billable Hours",
      db_field: "weekly_billable_hours",
      selector: (row) => row.weekly_billable_hours || "",
      omit: false,
      sortable: true,
      filterable: true,
    },
  ], [currentPage, perPage]);

  // --- Filter columns to only show the ones in VISIBLE_COLUMNS ---
  const visibleColumns = useMemo(() => {
    return allColumns.map(column => {
      if (VISIBLE_COLUMNS.includes(column.name)) {
        return { ...column, omit: false };
      } else {
        return { ...column, omit: true };
      }
    });
  }, [allColumns]);

  // --- Filter columns for SearchFilter to only include visible AND filterable columns ---
  const filterableColumnsForFinder = useMemo(() => {
    return visibleColumns.filter(col => col.filterable !== false);
  }, [visibleColumns]);

  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).forEach((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj });
    }
    setCurrentPage(1);
  };

  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      const result = await dispatch(
        userDataApi.endpoints.getUserData.initiate({
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10,
          query: queryString,
        })
      ).unwrap();

      if (!result?.total || result.total < 1) {
        return false;
      }
      var sl = 1;
      let prepXlsData = result.data.map((item) => {
        if (visibleColumns.length) {
          let obj = {};
          visibleColumns.forEach((column) => {
            if (!column.omit && column.selector) {
              obj[column.name] = column.name === "S.No" ? sl++ : column.selector(item) || "";
            }
          });
          return obj;
        }
      });

      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(blob, `${MODULE_NAME.replace(/ /g, "_")}_${prepXlsData.length}.xlsx`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };

  const fetchDataOptionsForFilterBy = useCallback(
    async (
      itemObject = {},
      type = "group",
      searching = "",
      fieldType = "select"
    ) => {
      let groupByField = itemObject.db_field || "title";
      let tableNameForFilter = itemObject.tableNameForFilter || "";

      try {
        setShowFilterOption(groupByField);
        setFilterOptionLoading(true);
        var groupData = [];
        const response = await triggerFilterByFetch({
          type: type.trim(),
          column: groupByField.trim(),
          table: tableNameForFilter.trim(),
          text: searching.trim(),
        });

        if (response.data) {
          groupData = response.data;
        }

        if (groupData.length) {
          if (fieldType === "searchable") {
            setFilterOptions((prev) => ({
              ...prev,
              [groupByField]: groupData,
            }));
            return groupData;
          }

          const optionsForFilter = groupData
            .map((item) => {
              if (itemObject.selector) {
                let label = "";
                let value = item[groupByField];
                if (itemObject?.selectorForFilter) {
                  label = itemObject.selectorForFilter(item);
                  value = item.id; 
                } else {
                  label = itemObject.selector(item);
                }
                if (label) {
                  if (item.total && item.total > 1) {
                    label += ` (${item.total})`;
                  }
                  return { label, value };
                }
                return null;
              }
              return null;
            })
            .filter(Boolean);

          const sortedOptions = sortByLabel(optionsForFilter);
          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: sortedOptions, // Use itemObject.id as key
          }));
          return sortedOptions;
        } else {
          // Clear options if no data
          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: [],
          }));
          return [];
        }
      } catch (error) {
        setError(error.message || "Failed to fetch filter options");
        return [];
      } finally {
        setFilterOptionLoading(false);
      }
    },
    [triggerFilterByFetch]
  );

  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      <div className="mx-auto pb-6 ">
        {/* Header section with title and action buttons */}
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div className="w-4/12 md:w-10/12 text-start">
            <h2 className="text-2xl font-bold ">{MODULE_NAME}</h2>
          </div>
          <div className="w-8/12 flex items-end justify-end gap-1">
            {/* Manage Columns dropdown - Shows all columns for toggling */}
            <ManageColumns columns={allColumns} setColumns={() => {}} />
            {/* Export to Excel button */}
            {!isFetching && dataItems && parseInt(dataItems.total) > 0 && (
              <>
                <button
                  className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={exportToExcel}
                >
                  {isFetching && (
                    <>
                      <span className="material-symbols-outlined animate-spin text-sm me-2">
                        progress_activity
                      </span>
                    </>
                  )}
                  {!isFetching && (
                    <span className="material-symbols-outlined text-sm me-2">
                      file_export
                    </span>
                  )}
                  Export to Excel ({dataItems.total})
                </button>
              </>
            )}
          </div>
        </div>

        {/* Filter fieldset for global search and field-specific filtering */}
        {/* Pass only the filterable visible columns to SearchFilter */}
        <SearchFilter
          columns={filterableColumnsForFinder} // Only pass filterable visible columns
          selectedFilterOptions={selectedFilterOptions}
          setSelectedFilterOptions={setSelectedFilterOptions}
          fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}
          filterOptions={filterOptions}
          filterOptionLoading={filterOptionLoading}
          showFilterOption={showFilterOption}
          resetPage={resetPage}
          setCurrentPage={setCurrentPage}
          buildQueryParams={buildQueryParams}
        />

        {/* Display error message if any error occurs */}
        {fetchError && <div className="text-red-500">Error fetching data: {fetchError.message || "Unknown error"}</div>}

        {/* Show loading spinner when data is being fetched */}
        {isFetching && <Loading />}

        {/* Dynamic Summary Data Section */}
        {!isFetching && dataItems && (
          <fieldset className="w-full border border-gray-200 text-start rounded-lg p-2 relative z-0 my-3">
            <legend className="ms-2 font-semibold text-sm py-1 px-2 flex">
              Summary Data
            </legend>
            <div className="flex flex-wrap">
              <div className={summaryReportBtn}>
                <span className="font-normal">Team Lead</span>:&nbsp;
                <span>{summaryData.teamLead}</span>
              </div>
              <div className={summaryReportBtn}>
                <span className="font-normal">Live Member</span>:&nbsp;
                <span>{summaryData.liveMembers}</span>
              </div>
              <div className={summaryReportBtn}>
                <span className="font-normal">Bench Member</span>:&nbsp;
                <span>{summaryData.benchMembers}</span>
              </div>
              <div className={summaryReportBtn}>
                <span className="font-normal">Total Designer</span>:&nbsp;
                <span>{summaryData.totalDesigners}</span>
              </div>
              <div className={summaryReportBtn}>
                <span className="font-normal">Total Developer</span>:&nbsp;
                <span>{summaryData.totalDevelopers}</span>
              </div>
              <div className={summaryReportBtn}>
                <span className="font-normal">Total QA</span>:&nbsp;
                <span>{summaryData.totalQA}</span>
              </div>
            </div>
          </fieldset>
        )}

        {/* Render the DataTable with the VISIBLE columns only */}
        <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
          <DataTable
            columns={visibleColumns}
            data={dataItems?.data || []}
            className="p-0 scrollbar-horizontal-10"
            fixedHeader
            highlightOnHover
            responsive
            pagination
            paginationServer
            paginationPerPage={perPage}
            paginationTotalRows={dataItems?.total || 0}
            onChangePage={(page) => {
              if (page !== currentPage) {
                setCurrentPage(page);
              }
            }}
            onChangeRowsPerPage={(newPerPage) => {
              if (newPerPage !== perPage) {
                setPerPage(newPerPage);
                setCurrentPage(1);
              }
            }}
            paginationComponentOptions={{
              selectAllRowsItem: true,
              selectAllRowsItemText: "ALL",
            }}
            sortServer
            onSort={(column, sortDirectionParam = "desc") => {
              if (Object.keys(column).length) {
                setSortColumn(column.db_field || column.name || "created_at");
                setSortDirection(sortDirectionParam || "desc");
              }
            }}
          />
        </div>

        {viewData && (
          <TableView item={viewData} setViewData={setViewData} columns={visibleColumns} />
        )}
      </div>
    </section>
  );
};

export default TeamSnapshotDataList;