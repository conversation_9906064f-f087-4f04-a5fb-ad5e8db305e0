{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team-snapshot\\\\TeamSnapshotDataList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect, useMemo } from \"react\";\n// DataTable component for rendering tabular data with features like pagination and sorting\nimport DataTable from \"react-data-table-component\";\n// Loading spinner component to show while data is loading\nimport Loading from \"./../../common/Loading\";\nimport { confirmationAlert, ManageColumns, SearchFilter, TableView, Image } from './../../common/coreui';\nimport { useDispatch } from \"react-redux\";\nimport { defaultDateFormat, defaultDateTimeFormat, removeKeys, sortByLabel, secondsToHours } from \"./../../utils\";\n// Libraries for exporting data to Excel\nimport { saveAs } from \"file-saver\";\nimport * as XLSX from \"xlsx\";\nimport { userData<PERSON>pi, useDeleteUserDataMutation, useGetUserDataQuery, useLazyFetchDataOptionsForUserDataQuery } from \"./../../features/api/userDataApi\";\nimport { useNavigate } from \"react-router-dom\";\nimport { DateTimeFormatDay, DateTimeFormatHour, DateTimeFormatTable } from \"./../../common/DateTimeFormatTable\";\nimport { useRoleBasedAccess } from \"../../common/useRoleBasedAccess\";\n\n// API endpoint and configuration constants\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MODULE_NAME = \"Team Snapshot\";\n\n// Define the list of columns that should be visible\nconst VISIBLE_COLUMNS = [\"Action\", \"S.No\", \"EID\", \"Full Name\", \"Designation\", \"Email\", \"Responsibility Label\", \"Department\",\n// Maps to 'departments'\n\"Team\",\n// Maps to 'teams'\n\"Team Lead\",\n// Maps to 'team_lead'\n\"Desk ID\",\n// Maps to 'desk_id'\n\"Billing Status\",\n// Maps to 'billing_statuses'\n\"Resource Status\",\n// Maps to 'resource_statuses'\n\"Contract Type\",\n// Maps to 'contact_types'\n\"Available Status\",\n// Maps to 'available_statuses'\n\"Work Location\",\n// Maps to 'locations'\n\"Office Branch\",\n// Maps to 'branches'\n\"On-Site Status\",\n// Maps to 'onsite_statuses'\n\"Team Member Status\" // Maps to 'member_statuses'\n];\n\n// Main component for listing Team Snapshot Data\nconst TeamSnapshotDataList = () => {\n  _s();\n  // State variables for data items, filters, search text, modals, and loading status\n  const [filterOptions, setFilterOptions] = useState({});\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\n  const [queryString, setQueryString] = useState(\"\");\n  const [modalVisible, setModalVisible] = useState(false);\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\n  const [dataItemsId, setDataItemsId] = useState(null);\n  const [error, setError] = useState(null);\n  const [viewData, setViewData] = useState(null);\n  const navigate = useNavigate();\n  const {\n    rolePermissions\n  } = useRoleBasedAccess();\n  const summaryReportBtn = \"w-auto whitespace-nowrap m-1 h-[40px] py-2 px-2 text-sm font-medium text-gray-900 bg-green-100 rounded-lg border border-green-500 hover:bg-green-300 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 capitalize\";\n\n  // Sorting and pagination state\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\n  const [sortDirection, setSortDirection] = useState(\"desc\");\n  const [perPage, setPerPage] = useState(\"10\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const {\n    data: dataItems,\n    isFetching,\n    error: fetchError\n  } = useGetUserDataQuery({\n    sort_by: sortColumn,\n    order: sortDirection,\n    page: currentPage,\n    per_page: perPage,\n    query: queryString\n  });\n  const [triggerFilterByFetch] = useLazyFetchDataOptionsForUserDataQuery();\n  const [deleteUserData] = useDeleteUserDataMutation();\n\n  // --- State for Summary Data ---\n  const [summaryData, setSummaryData] = useState({\n    teamLead: \"N/A\",\n    liveMembers: 0,\n    benchMembers: 0,\n    totalDesigners: 0,\n    totalDevelopers: 0,\n    totalQA: 0\n  });\n\n  // --- Function to Calculate Summary Data ---\n  const calculateSummaryData = useCallback(items => {\n    if (!items || items.length === 0) {\n      setSummaryData({\n        teamLead: \"N/A\",\n        liveMembers: 0,\n        benchMembers: 0,\n        totalDesigners: 0,\n        totalDevelopers: 0,\n        totalQA: 0\n      });\n      return;\n    }\n    let liveCount = 0;\n    let benchCount = 0;\n    let designerCount = 0;\n    let developerCount = 0;\n    let qaCount = 0;\n    let leadName = \"N/A\";\n\n    // Find Team Lead (example logic - adjust based on your data structure)\n    const leadUser = items.find(item => item.designations && item.designations.some(designation => designation.name.toLowerCase().includes(\"lead\") || designation.name.toLowerCase().includes(\"manager\")));\n    if (leadUser) {\n      leadName = `${leadUser.fname || ''} ${leadUser.lname || ''}`.trim() || leadUser.fname || \"N/A\";\n    }\n    items.forEach(item => {\n      var _item$resource_status, _item$designations;\n      // Member Status Counts\n      const statusNames = ((_item$resource_status = item.resource_statuses) === null || _item$resource_status === void 0 ? void 0 : _item$resource_status.map(s => s.name.toLowerCase())) || [];\n      if (statusNames.includes('live') || statusNames.includes('active')) {\n        liveCount++;\n      } else if (statusNames.includes('bench')) {\n        benchCount++;\n      }\n\n      // Role Counts\n      const designationNames = ((_item$designations = item.designations) === null || _item$designations === void 0 ? void 0 : _item$designations.map(d => d.name.toLowerCase())) || [];\n      if (designationNames.some(name => name.includes('designer'))) {\n        designerCount++;\n      }\n      if (designationNames.some(name => name.includes('developer'))) {\n        developerCount++;\n      }\n      if (designationNames.some(name => name.includes('qa') || name.includes('quality'))) {\n        qaCount++;\n      }\n    });\n    setSummaryData({\n      teamLead: leadName,\n      liveMembers: liveCount,\n      benchMembers: benchCount,\n      totalDesigners: designerCount,\n      totalDevelopers: developerCount,\n      totalQA: qaCount\n    });\n  }, []);\n  useEffect(() => {\n    if (dataItems !== null && dataItems !== void 0 && dataItems.data) {\n      calculateSummaryData(dataItems.data);\n    } else {\n      calculateSummaryData([]);\n    }\n  }, [dataItems, calculateSummaryData]);\n\n  // Build query parameters from selected filters\n  const buildQueryParams = selectedFilters => {\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\n      if (typeof value === \"string\") {\n        return acc + `&${key}=${value}`;\n      }\n      if (Array.isArray(value)) {\n        const vals = value.map(i => i.value).join(\",\");\n        return acc + `&${key}=${vals}`;\n      }\n      return acc;\n    }, \"\");\n    setQueryString(q);\n  };\n  const handleEdit = id => {\n    setViewData(null);\n    setDataItemsId(id);\n    setModalVisible(true);\n  };\n  const handleDelete = id => {\n    confirmationAlert({\n      onConfirm: () => {\n        deleteUserData(id);\n        setViewData(null);\n      }\n    });\n  };\n  let columnSerial = 1;\n  const allColumns = useMemo(() => [{\n    id: columnSerial++,\n    name: \"Action\",\n    width: \"100px\",\n    cell: item => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => {\n          setViewData(item);\n        },\n        title: \"View\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"visibility\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: columnSerial++,\n    name: \"S.No\",\n    selector: (row, index) => (currentPage - 1) * perPage + index + 1,\n    width: \"80px\",\n    omit: false\n  }, {\n    id: columnSerial++,\n    name: \"EID\",\n    db_field: \"eid\",\n    selector: row => row.eid || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Full Name\",\n    width: '300px',\n    db_field: \"id\",\n    selector: row => row !== null && row !== void 0 && row.fname ? `${row === null || row === void 0 ? void 0 : row.fname} ${row === null || row === void 0 ? void 0 : row.lname}` : \"\",\n    cell: row => {\n      var _row$designations;\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"flex align-middle items-center w-full text-start gap-2 px-4 py-2\",\n        children: [row !== null && row !== void 0 && row.photo ? /*#__PURE__*/_jsxDEV(Image, {\n          src: `${process.env.REACT_APP_BASE_STORAGE_URL}/${row === null || row === void 0 ? void 0 : row.photo}`,\n          alt: \"Profile Photo\",\n          className: \"w-10 h-10 rounded-full object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\",\n          children: [\"No \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 154\n          }, this), \"Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col min-w-[80%]\",\n          children: [(row === null || row === void 0 ? void 0 : row.fname) && /*#__PURE__*/_jsxDEV(\"b\", {\n            children: [row.fname, \" \", row.lname]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 28\n          }, this), (row === null || row === void 0 ? void 0 : (_row$designations = row.designations) === null || _row$designations === void 0 ? void 0 : _row$designations.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: row.designations.map(designation => designation.name).join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-400\",\n            children: \"N/A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this);\n    },\n    sortable: true,\n    omit: false,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Email\",\n    db_field: \"email\",\n    selector: row => row.email || \"\",\n    cell: row => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"lowercase\",\n      children: row.email || \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 22\n    }, this),\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Responsibility Label\",\n    db_field: \"resource_types\",\n    selector: row => {\n      var _row$resource_types;\n      return ((_row$resource_types = row.resource_types) === null || _row$resource_types === void 0 ? void 0 : _row$resource_types.map(type => type.name).join(', ')) || \"N/A\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'resource_types',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Department\",\n    db_field: \"departments\",\n    selector: row => {\n      var _row$departments;\n      return ((_row$departments = row.departments) === null || _row$departments === void 0 ? void 0 : _row$departments.map(department => department.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'departments',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Team\",\n    db_field: \"teams\",\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'teams',\n    omit: false,\n    sortable: true,\n    filterable: true,\n    className: 'team-column',\n    cell: row => {\n      var _row$team_names;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-cell-index\",\n        children: ((_row$team_names = row.team_names) === null || _row$team_names === void 0 ? void 0 : _row$team_names.join(', ')) || \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    id: columnSerial++,\n    name: \"Team Lead\",\n    db_field: \"team_lead\",\n    selector: row => {\n      const teamLead = row.teams && row.teams[0] ? row.teams[0].team_lead : null;\n      return teamLead || \"N/A\";\n    },\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Desk ID\",\n    db_field: \"desk_id\",\n    selector: row => row.desk_id || \"N/A\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Billing Status\",\n    db_field: \"billing_statuses\",\n    selector: row => {\n      var _row$billing_statuses;\n      return ((_row$billing_statuses = row.billing_statuses) === null || _row$billing_statuses === void 0 ? void 0 : _row$billing_statuses.map(status => status.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'billing_statuses',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Resource Status\",\n    db_field: \"resource_statuses\",\n    selector: row => {\n      var _row$resource_statuse;\n      return ((_row$resource_statuse = row.resource_statuses) === null || _row$resource_statuse === void 0 ? void 0 : _row$resource_statuse.map(status => status.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'resource_statuses',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Contract Type\",\n    db_field: \"contact_types\",\n    selector: row => {\n      var _row$contact_types;\n      return ((_row$contact_types = row.contact_types) === null || _row$contact_types === void 0 ? void 0 : _row$contact_types.map(contact => contact.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'contact_types',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Available Status\",\n    db_field: \"available_statuses\",\n    selector: row => {\n      var _row$available_status;\n      return ((_row$available_status = row.available_statuses) === null || _row$available_status === void 0 ? void 0 : _row$available_status.map(status => status.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'available_statuses',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Work Location\",\n    db_field: \"locations\",\n    selector: row => {\n      var _row$branches;\n      const locations = (row === null || row === void 0 ? void 0 : (_row$branches = row.branches) === null || _row$branches === void 0 ? void 0 : _row$branches.flatMap(branch => {\n        var _branch$locations;\n        return (_branch$locations = branch.locations) === null || _branch$locations === void 0 ? void 0 : _branch$locations.map(location => location.locations_name);\n      })) || [];\n      return locations.join(', ') || \"\";\n    },\n    selectorForFilter: row => row.locations_name || \"N/A\",\n    tableNameForFilter: 'locations',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Office Branch\",\n    db_field: \"branches\",\n    selector: row => {\n      var _row$branches2;\n      return ((_row$branches2 = row.branches) === null || _row$branches2 === void 0 ? void 0 : _row$branches2.map(branch => branch.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'branches',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"On-Site Status\",\n    db_field: \"onsite_statuses\",\n    selector: row => {\n      var _row$onsite_statuses;\n      return ((_row$onsite_statuses = row.onsite_statuses) === null || _row$onsite_statuses === void 0 ? void 0 : _row$onsite_statuses.map(status => status.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'onsite_statuses',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Team Member Status\",\n    db_field: \"member_statuses\",\n    selector: row => {\n      var _row$member_statuses;\n      return ((_row$member_statuses = row.member_statuses) === null || _row$member_statuses === void 0 ? void 0 : _row$member_statuses.map(status => status.name).join(', ')) || \"\";\n    },\n    selectorForFilter: row => row.name || \"N/A\",\n    tableNameForFilter: 'member_statuses',\n    omit: false,\n    sortable: true,\n    filterable: true\n  }], [currentPage, perPage]);\n  const visibleColumns = useMemo(() => {\n    return allColumns.map(column => {\n      if (VISIBLE_COLUMNS.includes(column.name)) {\n        return {\n          ...column,\n          omit: false\n        };\n      } else {\n        return {\n          ...column,\n          omit: true\n        };\n      }\n    });\n  }, [allColumns]);\n\n  // --- Filter columns for SearchFilter to only include visible AND filterable columns ---\n  const filterableColumnsForFinder = useMemo(() => {\n    return visibleColumns.filter(col => col.filterable !== false);\n  }, [visibleColumns]);\n\n  // Resets the pagination and clear-all filter state\n  const resetPage = () => {\n    if (Object.keys(selectedFilterOptions).length) {\n      let newObj = {};\n      Object.keys(selectedFilterOptions).forEach(key => {\n        if (typeof selectedFilterOptions[key] === \"string\") {\n          newObj[key] = \"\";\n        } else {\n          newObj[key] = [];\n        }\n      });\n      setSelectedFilterOptions({\n        ...newObj\n      });\n      buildQueryParams({\n        ...newObj\n      });\n    }\n    setCurrentPage(1);\n  };\n\n  // Export the fetched data into an Excel file\n  const dispatch = useDispatch();\n  const exportToExcel = async () => {\n    try {\n      const result = await dispatch(userDataApi.endpoints.getUserData.initiate({\n        sort_by: sortColumn,\n        order: sortDirection,\n        page: currentPage,\n        per_page: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 10,\n        query: queryString\n      })).unwrap();\n      if (!(result !== null && result !== void 0 && result.total) || result.total < 1) {\n        return false;\n      }\n      var sl = 1;\n      let prepXlsData = result.data.map(item => {\n        if (visibleColumns.length) {\n          let obj = {};\n          visibleColumns.forEach(column => {\n            if (!column.omit && column.selector) {\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\n            }\n          });\n          return obj;\n        }\n      });\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\n      const workbook = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\n      const excelBuffer = XLSX.write(workbook, {\n        bookType: \"xlsx\",\n        type: \"array\"\n      });\n      const blob = new Blob([excelBuffer], {\n        type: \"application/octet-stream\"\n      });\n      saveAs(blob, `${MODULE_NAME.replace(/ /g, \"_\")}_${prepXlsData.length}.xlsx`);\n    } catch (error) {\n      console.error(\"Error exporting to Excel:\", error);\n    }\n  };\n  const fetchDataOptionsForFilterBy = useCallback(async (itemObject = {}, type = \"group\", searching = \"\", fieldType = \"select\") => {\n    let groupByField = itemObject.db_field || \"title\";\n    let tableNameForFilter = itemObject.tableNameForFilter || \"\";\n    try {\n      setShowFilterOption(groupByField);\n      setFilterOptionLoading(true);\n      var groupData = [];\n      const response = await triggerFilterByFetch({\n        type: type.trim(),\n        column: groupByField.trim(),\n        table: tableNameForFilter.trim(),\n        text: searching.trim()\n      });\n      if (response.data) {\n        groupData = response.data;\n      }\n      if (groupData.length) {\n        if (fieldType === \"searchable\") {\n          setFilterOptions(prev => ({\n            ...prev,\n            [groupByField]: groupData\n          }));\n          return groupData;\n        }\n        const optionsForFilter = groupData.map(item => {\n          if (itemObject.selector) {\n            let label = \"\";\n            let value = item[groupByField];\n            if (itemObject !== null && itemObject !== void 0 && itemObject.selectorForFilter) {\n              label = itemObject.selectorForFilter(item);\n              value = item.id;\n            } else {\n              label = itemObject.selector(item);\n            }\n            if (label) {\n              if (item.total && item.total > 1) {\n                label += ` (${item.total})`;\n              }\n              return {\n                label,\n                value\n              };\n            }\n            return null;\n          }\n          return null;\n        }).filter(Boolean);\n        const sortedOptions = sortByLabel(optionsForFilter);\n        setFilterOptions(prev => ({\n          ...prev,\n          [itemObject.id]: sortedOptions // Use itemObject.id as key\n        }));\n        return sortedOptions;\n      } else {\n        // Clear options if no data\n        setFilterOptions(prev => ({\n          ...prev,\n          [itemObject.id]: []\n        }));\n        return [];\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to fetch filter options\");\n      return [];\n    } finally {\n      setFilterOptionLoading(false);\n    }\n  }, [triggerFilterByFetch]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-auto pb-6 \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4/12 md:w-10/12 text-start\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold \",\n            children: MODULE_NAME\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8/12 flex items-end justify-end gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(ManageColumns, {\n            columns: allColumns,\n            setColumns: () => {}\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this), !isFetching && dataItems && parseInt(dataItems.total) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",\n              onClick: exportToExcel,\n              children: [isFetching && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-outlined animate-spin text-sm me-2\",\n                  children: \"progress_activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false), !isFetching && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm me-2\",\n                children: \"file_export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this), \"Export to Excel (\", dataItems.total, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this)\n          }, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchFilter, {\n        columns: filterableColumnsForFinder // Only pass filterable visible columns\n        ,\n        selectedFilterOptions: selectedFilterOptions,\n        setSelectedFilterOptions: setSelectedFilterOptions,\n        fetchDataOptionsForFilterBy: fetchDataOptionsForFilterBy,\n        filterOptions: filterOptions,\n        filterOptionLoading: filterOptionLoading,\n        showFilterOption: showFilterOption,\n        resetPage: resetPage,\n        setCurrentPage: setCurrentPage,\n        buildQueryParams: buildQueryParams\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this), fetchError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500\",\n        children: [\"Error fetching data: \", fetchError.message || \"Unknown error\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 24\n      }, this), isFetching && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 24\n      }, this), !isFetching && dataItems && /*#__PURE__*/_jsxDEV(\"fieldset\", {\n        className: \"w-full border border-gray-200 text-start rounded-lg p-2 relative z-0 my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"legend\", {\n          className: \"ms-2 font-semibold text-sm py-1 px-2 flex\",\n          children: \"Summary Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Team Lead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.teamLead\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Live Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.liveMembers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Bench Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.benchMembers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Total Designer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.totalDesigners\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Total Developer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.totalDevelopers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: summaryReportBtn,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-normal\",\n              children: \"Total QA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 17\n            }, this), \":\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: summaryData.totalQA\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 p-0 pb-1 rounded-lg my-5 \",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          columns: visibleColumns,\n          data: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [],\n          className: \"p-0 scrollbar-horizontal-10\",\n          fixedHeader: true,\n          highlightOnHover: true,\n          responsive: true,\n          pagination: true,\n          paginationServer: true,\n          paginationPerPage: perPage,\n          paginationTotalRows: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 0,\n          onChangePage: page => {\n            if (page !== currentPage) {\n              setCurrentPage(page);\n            }\n          },\n          onChangeRowsPerPage: newPerPage => {\n            if (newPerPage !== perPage) {\n              setPerPage(newPerPage);\n              setCurrentPage(1);\n            }\n          },\n          paginationComponentOptions: {\n            selectAllRowsItem: true,\n            selectAllRowsItemText: \"ALL\"\n          },\n          sortServer: true,\n          onSort: (column, sortDirectionParam = \"desc\") => {\n            if (Object.keys(column).length) {\n              setSortColumn(column.db_field || column.name || \"created_at\");\n              setSortDirection(sortDirectionParam || \"desc\");\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this), viewData && /*#__PURE__*/_jsxDEV(TableView, {\n        item: viewData,\n        setViewData: setViewData,\n        columns: visibleColumns\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 581,\n    columnNumber: 5\n  }, this);\n};\n_s(TeamSnapshotDataList, \"/k1miKkHlWIytcdmyL5J3v9OWtw=\", false, function () {\n  return [useNavigate, useRoleBasedAccess, useGetUserDataQuery, useLazyFetchDataOptionsForUserDataQuery, useDeleteUserDataMutation, useDispatch];\n});\n_c = TeamSnapshotDataList;\nexport default TeamSnapshotDataList;\nvar _c;\n$RefreshReg$(_c, \"TeamSnapshotDataList\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "useMemo", "DataTable", "Loading", "<PERSON><PERSON><PERSON><PERSON>", "ManageColumns", "SearchFilter", "TableView", "Image", "useDispatch", "defaultDateFormat", "defaultDateTimeFormat", "<PERSON><PERSON><PERSON><PERSON>", "sortByLabel", "secondsToHours", "saveAs", "XLSX", "userDataApi", "useDeleteUserDataMutation", "useGetUserDataQuery", "useLazyFetchDataOptionsForUserDataQuery", "useNavigate", "DateTimeFormatDay", "DateTimeFormatHour", "DateTimeFormatTable", "useRoleBasedAccess", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MODULE_NAME", "VISIBLE_COLUMNS", "TeamSnapshotDataList", "_s", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "dataItemsId", "setDataItemsId", "error", "setError", "viewData", "setViewData", "navigate", "rolePermissions", "summaryReportBtn", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "deleteUserData", "summaryData", "setSummaryData", "teamLead", "liveMembers", "benchMembers", "totalDesigners", "totalDevelopers", "totalQA", "calculateSummaryData", "items", "length", "liveCount", "benchCount", "designerCount", "developerCount", "qaCount", "leadName", "leadUser", "find", "item", "designations", "some", "designation", "name", "toLowerCase", "includes", "fname", "lname", "trim", "for<PERSON>ach", "_item$resource_status", "_item$designations", "statusNames", "resource_statuses", "map", "s", "designationNames", "d", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "value", "Array", "isArray", "vals", "i", "join", "handleEdit", "id", "handleDelete", "onConfirm", "columnSerial", "allColumns", "width", "cell", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selector", "row", "index", "omit", "db_field", "eid", "sortable", "filterable", "_row$designations", "photo", "src", "process", "env", "REACT_APP_BASE_STORAGE_URL", "alt", "email", "_row$resource_types", "resource_types", "type", "selectorFor<PERSON>ilter", "tableNameForFilter", "_row$departments", "departments", "department", "_row$team_names", "team_names", "teams", "team_lead", "desk_id", "_row$billing_statuses", "billing_statuses", "status", "_row$resource_statuse", "_row$contact_types", "contact_types", "contact", "_row$available_status", "available_statuses", "_row$branches", "locations", "branches", "flatMap", "branch", "_branch$locations", "location", "locations_name", "_row$branches2", "_row$onsite_statuses", "onsite_statuses", "_row$member_statuses", "member_statuses", "visibleColumns", "column", "filterableColumnsForFinder", "filter", "col", "resetPage", "keys", "newObj", "dispatch", "exportToExcel", "result", "endpoints", "getUserData", "initiate", "total", "unwrap", "sl", "prepXlsData", "obj", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "write", "bookType", "blob", "Blob", "replace", "console", "fetchDataOptionsForFilterBy", "itemObject", "searching", "fieldType", "groupByField", "groupData", "response", "table", "text", "prev", "optionsForFilter", "label", "Boolean", "sortedOptions", "message", "columns", "setColumns", "parseInt", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "sortDirectionParam", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team-snapshot/TeamSnapshotDataList.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect, useMemo } from \"react\";\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\nimport { confirmationAlert, ManageColumns, SearchFilter, TableView, Image } from './../../common/coreui';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateFormat, defaultDateTimeFormat, removeKeys, sortByLabel, secondsToHours } from \"./../../utils\";\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { userDataApi, useDeleteUserDataMutation, useGetUserDataQuery, useLazyFetchDataOptionsForUserDataQuery } from \"./../../features/api/userDataApi\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour, DateTimeFormatTable } from \"./../../common/DateTimeFormatTable\";\r\nimport { useRoleBasedAccess } from \"../../common/useRoleBasedAccess\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Team Snapshot\";\r\n\r\n// Define the list of columns that should be visible\r\nconst VISIBLE_COLUMNS = [\r\n  \"Action\",\r\n  \"S.No\",\r\n  \"EID\",\r\n  \"Full Name\",\r\n  \"Designation\",\r\n  \"Email\",\r\n  \"Responsibility Label\", \r\n  \"Department\", // Maps to 'departments'\r\n  \"Team\", // Maps to 'teams'\r\n  \"Team Lead\", // Maps to 'team_lead'\r\n  \"Desk ID\", // Maps to 'desk_id'\r\n  \"Billing Status\", // Maps to 'billing_statuses'\r\n  \"Resource Status\", // Maps to 'resource_statuses'\r\n  \"Contract Type\", // Maps to 'contact_types'\r\n  \"Available Status\", // Maps to 'available_statuses'\r\n  \"Work Location\", // Maps to 'locations'\r\n  \"Office Branch\", // Maps to 'branches'\r\n  \"On-Site Status\", // Maps to 'onsite_statuses'\r\n  \"Team Member Status\", // Maps to 'member_statuses'\r\n];\r\n\r\n// Main component for listing Team Snapshot Data\r\nconst TeamSnapshotDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n  const summaryReportBtn = \"w-auto whitespace-nowrap m-1 h-[40px] py-2 px-2 text-sm font-medium text-gray-900 bg-green-100 rounded-lg border border-green-500 hover:bg-green-300 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 capitalize\";\r\n\r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  const { data: dataItems, isFetching, error: fetchError } = useGetUserDataQuery({\r\n    sort_by: sortColumn,\r\n    order: sortDirection,\r\n    page: currentPage,\r\n    per_page: perPage,\r\n    query: queryString,\r\n  });\r\n\r\n  const [triggerFilterByFetch] = useLazyFetchDataOptionsForUserDataQuery();\r\n  const [deleteUserData] = useDeleteUserDataMutation();\r\n\r\n  // --- State for Summary Data ---\r\n  const [summaryData, setSummaryData] = useState({\r\n    teamLead: \"N/A\",\r\n    liveMembers: 0,\r\n    benchMembers: 0,\r\n    totalDesigners: 0,\r\n    totalDevelopers: 0,\r\n    totalQA: 0,\r\n  });\r\n\r\n  // --- Function to Calculate Summary Data ---\r\n  const calculateSummaryData = useCallback((items) => {\r\n    if (!items || items.length === 0) {\r\n      setSummaryData({\r\n        teamLead: \"N/A\",\r\n        liveMembers: 0,\r\n        benchMembers: 0,\r\n        totalDesigners: 0,\r\n        totalDevelopers: 0,\r\n        totalQA: 0,\r\n      });\r\n      return;\r\n    }\r\n\r\n    let liveCount = 0;\r\n    let benchCount = 0;\r\n    let designerCount = 0;\r\n    let developerCount = 0;\r\n    let qaCount = 0;\r\n    let leadName = \"N/A\";\r\n\r\n    // Find Team Lead (example logic - adjust based on your data structure)\r\n    const leadUser = items.find(item =>\r\n      item.designations &&\r\n      item.designations.some(designation =>\r\n        designation.name.toLowerCase().includes(\"lead\") ||\r\n        designation.name.toLowerCase().includes(\"manager\")\r\n      )\r\n    );\r\n\r\n    if (leadUser) {\r\n      leadName = `${leadUser.fname || ''} ${leadUser.lname || ''}`.trim() || leadUser.fname || \"N/A\";\r\n    }\r\n\r\n    items.forEach(item => {\r\n      // Member Status Counts\r\n      const statusNames = item.resource_statuses?.map(s => s.name.toLowerCase()) || [];\r\n      if (statusNames.includes('live') || statusNames.includes('active')) {\r\n        liveCount++;\r\n      } else if (statusNames.includes('bench')) {\r\n        benchCount++;\r\n      }\r\n\r\n      // Role Counts\r\n      const designationNames = item.designations?.map(d => d.name.toLowerCase()) || [];\r\n      if (designationNames.some(name => name.includes('designer'))) {\r\n        designerCount++;\r\n      }\r\n      if (designationNames.some(name => name.includes('developer'))) {\r\n        developerCount++;\r\n      }\r\n      if (designationNames.some(name => name.includes('qa') || name.includes('quality'))) {\r\n        qaCount++;\r\n      }\r\n    });\r\n\r\n    setSummaryData({\r\n      teamLead: leadName,\r\n      liveMembers: liveCount,\r\n      benchMembers: benchCount,\r\n      totalDesigners: designerCount,\r\n      totalDevelopers: developerCount,\r\n      totalQA: qaCount,\r\n    });\r\n  }, []);\r\n\r\n \r\n  useEffect(() => {\r\n    if (dataItems?.data) {\r\n      calculateSummaryData(dataItems.data);\r\n    } else {\r\n      calculateSummaryData([]);\r\n    }\r\n  }, [dataItems, calculateSummaryData]);\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\");\r\n    setQueryString(q);\r\n  };\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null);\r\n    setDataItemsId(id);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({\r\n      onConfirm: () => {\r\n        deleteUserData(id);\r\n        setViewData(null);\r\n      },\r\n    });\r\n  };\r\n\r\n  let columnSerial = 1;\r\n  const allColumns = useMemo(() => [\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"100px\",\r\n      cell: (item) => (\r\n        <div className=\"flex justify-center\">\r\n          <button\r\n            className=\"flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => {\r\n              setViewData(item);\r\n            }}\r\n            title=\"View\"\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"EID\",\r\n      db_field: \"eid\",\r\n      selector: (row) => row.eid || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Full Name\",\r\n      width: '300px',\r\n      db_field: \"id\",\r\n      selector: (row) => row?.fname ? `${row?.fname} ${row?.lname}` : \"\",\r\n      cell: (row) => (\r\n        <span className=\"flex align-middle items-center w-full text-start gap-2 px-4 py-2\">\r\n          {row?.photo ? (\r\n            <Image\r\n              src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row?.photo}`}\r\n              alt=\"Profile Photo\"\r\n              className=\"w-10 h-10 rounded-full object-cover\"\r\n            />\r\n          ) : (\r\n            <span className=\"min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full\">No <br />Photo</span>\r\n          )}\r\n          <div className=\"flex flex-col min-w-[80%]\">\r\n            {row?.fname && <b>{row.fname} {row.lname}</b>}\r\n            {row?.designations?.length > 0 ? (\r\n              <div>{row.designations.map(designation => designation.name).join(', ')}</div>\r\n            ) : (\r\n              <div className=\"text-sm text-gray-400\">N/A</div>\r\n            )}\r\n          </div>\r\n        </span>\r\n      ),\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Email\",\r\n      db_field: \"email\",\r\n      selector: (row) => row.email || \"\",\r\n      cell: (row) => <span className=\"lowercase\">{row.email || \"\"}</span>,\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Responsibility Label\",\r\n      db_field: \"resource_types\",\r\n      selector: (row) => row.resource_types?.map(type => type.name).join(', ') || \"N/A\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'resource_types',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Department\",\r\n      db_field: \"departments\",\r\n      selector: (row) => row.departments?.map(department => department.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'departments',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team\",\r\n      db_field: \"teams\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'teams',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n      className: 'team-column',\r\n      cell: (row) => (\r\n        <div className=\"team-cell-index\">\r\n          {row.team_names?.join(', ') || \"N/A\"}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Lead\",\r\n      db_field: \"team_lead\",\r\n      selector: (row) => {\r\n        const teamLead = row.teams && row.teams[0] ? row.teams[0].team_lead : null;\r\n        return teamLead || \"N/A\";\r\n      },\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Desk ID\",\r\n      db_field: \"desk_id\",\r\n      selector: (row) => row.desk_id || \"N/A\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Billing Status\",\r\n      db_field: \"billing_statuses\",\r\n      selector: (row) => row.billing_statuses?.map(status => status.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'billing_statuses',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Resource Status\",\r\n      db_field: \"resource_statuses\",\r\n      selector: (row) => row.resource_statuses?.map(status => status.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'resource_statuses',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Contract Type\",\r\n      db_field: \"contact_types\",\r\n      selector: (row) => row.contact_types?.map(contact => contact.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'contact_types',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Available Status\",\r\n      db_field: \"available_statuses\",\r\n      selector: (row) => row.available_statuses?.map(status => status.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'available_statuses',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Work Location\",\r\n      db_field: \"locations\",\r\n      selector: (row) => {\r\n        const locations = row?.branches?.flatMap(branch =>\r\n          branch.locations?.map(location => location.locations_name)\r\n        ) || [];\r\n        return locations.join(', ') || \"\";\r\n      },\r\n      selectorForFilter: (row) => row.locations_name || \"N/A\",\r\n      tableNameForFilter: 'locations',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Office Branch\",\r\n      db_field: \"branches\",\r\n      selector: (row) => row.branches?.map(branch => branch.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'branches',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"On-Site Status\",\r\n      db_field: \"onsite_statuses\",\r\n      selector: (row) => row.onsite_statuses?.map(status => status.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'onsite_statuses',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team Member Status\",\r\n      db_field: \"member_statuses\",\r\n      selector: (row) => row.member_statuses?.map(status => status.name).join(', ') || \"\",\r\n      selectorForFilter: (row) => row.name || \"N/A\",\r\n      tableNameForFilter: 'member_statuses',\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n  ], [currentPage, perPage]); \r\n\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.map(column => {\r\n      if (VISIBLE_COLUMNS.includes(column.name)) {\r\n        return { ...column, omit: false };\r\n      } else {\r\n        return { ...column, omit: true };\r\n      }\r\n    });\r\n  }, [allColumns]);\r\n\r\n  // --- Filter columns for SearchFilter to only include visible AND filterable columns ---\r\n  const filterableColumnsForFinder = useMemo(() => {\r\n    return visibleColumns.filter(col => col.filterable !== false);\r\n  }, [visibleColumns]);\r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).forEach((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj });\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      const result = await dispatch(\r\n        userDataApi.endpoints.getUserData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10,\r\n          query: queryString,\r\n        })\r\n      ).unwrap();\r\n\r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n      var sl = 1;\r\n      let prepXlsData = result.data.map((item) => {\r\n        if (visibleColumns.length) {\r\n          let obj = {};\r\n          visibleColumns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g, \"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n      let groupByField = itemObject.db_field || \"title\";\r\n      let tableNameForFilter = itemObject.tableNameForFilter || \"\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n        var groupData = [];\r\n        const response = await triggerFilterByFetch({\r\n          type: type.trim(),\r\n          column: groupByField.trim(),\r\n          table: tableNameForFilter.trim(),\r\n          text: searching.trim(),\r\n        });\r\n\r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if (itemObject.selector) {\r\n                let label = \"\";\r\n                let value = item[groupByField];\r\n                if (itemObject?.selectorForFilter) {\r\n                  label = itemObject.selectorForFilter(item);\r\n                  value = item.id; \r\n                } else {\r\n                  label = itemObject.selector(item);\r\n                }\r\n                if (label) {\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n                  return { label, value };\r\n                }\r\n                return null;\r\n              }\r\n              return null;\r\n            })\r\n            .filter(Boolean);\r\n\r\n          const sortedOptions = sortByLabel(optionsForFilter);\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortedOptions, // Use itemObject.id as key\r\n          }));\r\n          return sortedOptions;\r\n        } else {\r\n          // Clear options if no data\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: [],\r\n          }));\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        setError(error.message || \"Failed to fetch filter options\");\r\n        return [];\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    [triggerFilterByFetch]\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown - Shows all columns for toggling */}\r\n            <ManageColumns columns={allColumns} setColumns={() => {}} />\r\n            {/* Export to Excel button */}\r\n            {!isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                      file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        {/* Pass only the filterable visible columns to SearchFilter */}\r\n        <SearchFilter\r\n          columns={filterableColumnsForFinder} // Only pass filterable visible columns\r\n          selectedFilterOptions={selectedFilterOptions}\r\n          setSelectedFilterOptions={setSelectedFilterOptions}\r\n          fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n          filterOptions={filterOptions}\r\n          filterOptionLoading={filterOptionLoading}\r\n          showFilterOption={showFilterOption}\r\n          resetPage={resetPage}\r\n          setCurrentPage={setCurrentPage}\r\n          buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">Error fetching data: {fetchError.message || \"Unknown error\"}</div>}\r\n\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* Dynamic Summary Data Section */}\r\n        {!isFetching && dataItems && (\r\n          <fieldset className=\"w-full border border-gray-200 text-start rounded-lg p-2 relative z-0 my-3\">\r\n            <legend className=\"ms-2 font-semibold text-sm py-1 px-2 flex\">\r\n              Summary Data\r\n            </legend>\r\n            <div className=\"flex flex-wrap\">\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Team Lead</span>:&nbsp;\r\n                <span>{summaryData.teamLead}</span>\r\n              </div>\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Live Member</span>:&nbsp;\r\n                <span>{summaryData.liveMembers}</span>\r\n              </div>\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Bench Member</span>:&nbsp;\r\n                <span>{summaryData.benchMembers}</span>\r\n              </div>\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Total Designer</span>:&nbsp;\r\n                <span>{summaryData.totalDesigners}</span>\r\n              </div>\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Total Developer</span>:&nbsp;\r\n                <span>{summaryData.totalDevelopers}</span>\r\n              </div>\r\n              <div className={summaryReportBtn}>\r\n                <span className=\"font-normal\">Total QA</span>:&nbsp;\r\n                <span>{summaryData.totalQA}</span>\r\n              </div>\r\n            </div>\r\n          </fieldset>\r\n        )}\r\n\r\n        {/* Render the DataTable with the VISIBLE columns only */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={visibleColumns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if (newPerPage !== perPage) {\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirectionParam = \"desc\") => {\r\n              if (Object.keys(column).length) {\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirectionParam || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {viewData && (\r\n          <TableView item={viewData} setViewData={setViewData} columns={visibleColumns} />\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TeamSnapshotDataList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACxE;AACA,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,QAAQ,uBAAuB;AACxG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,cAAc,QAAQ,eAAe;AACjH;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,WAAW,EAAEC,yBAAyB,EAAEC,mBAAmB,EAAEC,uCAAuC,QAAQ,kCAAkC;AACvJ,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,oCAAoC;AAC/G,SAASC,kBAAkB,QAAQ,iCAAiC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAG,eAAe;;AAEnC;AACA,MAAMC,eAAe,GAAG,CACtB,QAAQ,EACR,MAAM,EACN,KAAK,EACL,WAAW,EACX,aAAa,EACb,OAAO,EACP,sBAAsB,EACtB,YAAY;AAAE;AACd,MAAM;AAAE;AACR,WAAW;AAAE;AACb,SAAS;AAAE;AACX,gBAAgB;AAAE;AAClB,iBAAiB;AAAE;AACnB,eAAe;AAAE;AACjB,kBAAkB;AAAE;AACpB,eAAe;AAAE;AACjB,eAAe;AAAE;AACjB,gBAAgB;AAAE;AAClB,oBAAoB,CAAE;AAAA,CACvB;;AAED;AACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtE,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMsD,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgC;EAAgB,CAAC,GAAG5B,kBAAkB,CAAC,CAAC;EAChD,MAAM6B,gBAAgB,GAAG,4UAA4U;;EAErW;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,YAAY,CAAC;EAC1D,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM;IAAEiE,IAAI,EAAEC,SAAS;IAAEC,UAAU;IAAEjB,KAAK,EAAEkB;EAAW,CAAC,GAAG/C,mBAAmB,CAAC;IAC7EgD,OAAO,EAAEZ,UAAU;IACnBa,KAAK,EAAEX,aAAa;IACpBY,IAAI,EAAER,WAAW;IACjBS,QAAQ,EAAEX,OAAO;IACjBY,KAAK,EAAE/B;EACT,CAAC,CAAC;EAEF,MAAM,CAACgC,oBAAoB,CAAC,GAAGpD,uCAAuC,CAAC,CAAC;EACxE,MAAM,CAACqD,cAAc,CAAC,GAAGvD,yBAAyB,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC;IAC7C8E,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAGnF,WAAW,CAAEoF,KAAK,IAAK;IAClD,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAChCT,cAAc,CAAC;QACbC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE,CAAC;QAClBC,OAAO,EAAE;MACX,CAAC,CAAC;MACF;IACF;IAEA,IAAII,SAAS,GAAG,CAAC;IACjB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,KAAK;;IAEpB;IACA,MAAMC,QAAQ,GAAGR,KAAK,CAACS,IAAI,CAACC,IAAI,IAC9BA,IAAI,CAACC,YAAY,IACjBD,IAAI,CAACC,YAAY,CAACC,IAAI,CAACC,WAAW,IAChCA,WAAW,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IAC/CH,WAAW,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CACnD,CACF,CAAC;IAED,IAAIR,QAAQ,EAAE;MACZD,QAAQ,GAAG,GAAGC,QAAQ,CAACS,KAAK,IAAI,EAAE,IAAIT,QAAQ,CAACU,KAAK,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC,IAAIX,QAAQ,CAACS,KAAK,IAAI,KAAK;IAChG;IAEAjB,KAAK,CAACoB,OAAO,CAACV,IAAI,IAAI;MAAA,IAAAW,qBAAA,EAAAC,kBAAA;MACpB;MACA,MAAMC,WAAW,GAAG,EAAAF,qBAAA,GAAAX,IAAI,CAACc,iBAAiB,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,KAAI,EAAE;MAChF,IAAIQ,WAAW,CAACP,QAAQ,CAAC,MAAM,CAAC,IAAIO,WAAW,CAACP,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAClEd,SAAS,EAAE;MACb,CAAC,MAAM,IAAIqB,WAAW,CAACP,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxCb,UAAU,EAAE;MACd;;MAEA;MACA,MAAMwB,gBAAgB,GAAG,EAAAL,kBAAA,GAAAZ,IAAI,CAACC,YAAY,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmBG,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACd,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,KAAI,EAAE;MAChF,IAAIY,gBAAgB,CAACf,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACE,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;QAC5DZ,aAAa,EAAE;MACjB;MACA,IAAIuB,gBAAgB,CAACf,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE;QAC7DX,cAAc,EAAE;MAClB;MACA,IAAIsB,gBAAgB,CAACf,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;QAClFV,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IAEFd,cAAc,CAAC;MACbC,QAAQ,EAAEc,QAAQ;MAClBb,WAAW,EAAEQ,SAAS;MACtBP,YAAY,EAAEQ,UAAU;MACxBP,cAAc,EAAEQ,aAAa;MAC7BP,eAAe,EAAEQ,cAAc;MAC/BP,OAAO,EAAEQ;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAGNzF,SAAS,CAAC,MAAM;IACd,IAAIgE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAED,IAAI,EAAE;MACnBmB,oBAAoB,CAAClB,SAAS,CAACD,IAAI,CAAC;IACtC,CAAC,MAAM;MACLmB,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC,EAAE,CAAClB,SAAS,EAAEkB,oBAAoB,CAAC,CAAC;;EAErC;EACA,MAAM8B,gBAAgB,GAAIC,eAAe,IAAK;IAC5C,IAAIC,CAAC,GAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOF,GAAG,GAAG,IAAIC,GAAG,IAAIC,KAAK,EAAE;MACjC;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACxB,MAAMG,IAAI,GAAGH,KAAK,CAACZ,GAAG,CAAEgB,CAAC,IAAKA,CAAC,CAACJ,KAAK,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;QAChD,OAAOP,GAAG,GAAG,IAAIC,GAAG,IAAII,IAAI,EAAE;MAChC;MACA,OAAOL,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IACN7E,cAAc,CAACyE,CAAC,CAAC;EACnB,CAAC;EAED,MAAMY,UAAU,GAAIC,EAAE,IAAK;IACzB5E,WAAW,CAAC,IAAI,CAAC;IACjBJ,cAAc,CAACgF,EAAE,CAAC;IAClBpF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqF,YAAY,GAAID,EAAE,IAAK;IAC3B3H,iBAAiB,CAAC;MAChB6H,SAAS,EAAEA,CAAA,KAAM;QACfxD,cAAc,CAACsD,EAAE,CAAC;QAClB5E,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI+E,YAAY,GAAG,CAAC;EACpB,MAAMC,UAAU,GAAGlI,OAAO,CAAC,MAAM,CAC/B;IACE8H,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,QAAQ;IACdmC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAGxC,IAAI,iBACTlE,OAAA;MAAK2G,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClC5G,OAAA;QACE2G,SAAS,EAAC,sKAAsK;QAChLE,OAAO,EAAEA,CAAA,KAAM;UACbrF,WAAW,CAAC0C,IAAI,CAAC;QACnB,CAAE;QACF4C,KAAK,EAAC,MAAM;QAAAF,QAAA,eAEZ5G,OAAA;UAAM2G,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAET,CAAC,EACD;IACEd,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,MAAM;IACZ6C,QAAQ,EAAEA,CAACC,GAAG,EAAEC,KAAK,KAAK,CAACnF,WAAW,GAAG,CAAC,IAAIF,OAAO,GAAGqF,KAAK,GAAG,CAAC;IACjEZ,KAAK,EAAE,MAAM;IACba,IAAI,EAAE;EACR,CAAC,EACD;IACElB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,KAAK;IACXiD,QAAQ,EAAE,KAAK;IACfJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACI,GAAG,IAAI,EAAE;IAChCF,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,WAAW;IACjBmC,KAAK,EAAE,OAAO;IACdc,QAAQ,EAAE,IAAI;IACdJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE3C,KAAK,GAAG,GAAG2C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE3C,KAAK,IAAI2C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE1C,KAAK,EAAE,GAAG,EAAE;IAClEgC,IAAI,EAAGU,GAAG;MAAA,IAAAO,iBAAA;MAAA,oBACR3H,OAAA;QAAM2G,SAAS,EAAC,kEAAkE;QAAAC,QAAA,GAC/EQ,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEQ,KAAK,gBACT5H,OAAA,CAACnB,KAAK;UACJgJ,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAIZ,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEQ,KAAK,EAAG;UAC/DK,GAAG,EAAC,eAAe;UACnBtB,SAAS,EAAC;QAAqC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,gBAEFlH,OAAA;UAAM2G,SAAS,EAAC,yHAAyH;UAAAC,QAAA,GAAC,KAAG,eAAA5G,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,SAAK;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC/J,eACDlH,OAAA;UAAK2G,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GACvC,CAAAQ,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE3C,KAAK,kBAAIzE,OAAA;YAAA4G,QAAA,GAAIQ,GAAG,CAAC3C,KAAK,EAAC,GAAC,EAAC2C,GAAG,CAAC1C,KAAK;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC5C,CAAAE,GAAG,aAAHA,GAAG,wBAAAO,iBAAA,GAAHP,GAAG,CAAEjD,YAAY,cAAAwD,iBAAA,uBAAjBA,iBAAA,CAAmBlE,MAAM,IAAG,CAAC,gBAC5BzD,OAAA;YAAA4G,QAAA,EAAMQ,GAAG,CAACjD,YAAY,CAACc,GAAG,CAACZ,WAAW,IAAIA,WAAW,CAACC,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI;UAAC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAE7ElH,OAAA;YAAK2G,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAChD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA,CACR;IACDO,QAAQ,EAAE,IAAI;IACdH,IAAI,EAAE,KAAK;IACXI,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,OAAO;IACbiD,QAAQ,EAAE,OAAO;IACjBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACc,KAAK,IAAI,EAAE;IAClCxB,IAAI,EAAGU,GAAG,iBAAKpH,OAAA;MAAM2G,SAAS,EAAC,WAAW;MAAAC,QAAA,EAAEQ,GAAG,CAACc,KAAK,IAAI;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IACnEI,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,sBAAsB;IAC5BiD,QAAQ,EAAE,gBAAgB;IAC1BJ,QAAQ,EAAGC,GAAG;MAAA,IAAAe,mBAAA;MAAA,OAAK,EAAAA,mBAAA,GAAAf,GAAG,CAACgB,cAAc,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBlD,GAAG,CAACoD,IAAI,IAAIA,IAAI,CAAC/D,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,KAAK;IAAA;IACjFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,gBAAgB;IACpCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,YAAY;IAClBiD,QAAQ,EAAE,aAAa;IACvBJ,QAAQ,EAAGC,GAAG;MAAA,IAAAoB,gBAAA;MAAA,OAAK,EAAAA,gBAAA,GAAApB,GAAG,CAACqB,WAAW,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBvD,GAAG,CAACyD,UAAU,IAAIA,UAAU,CAACpE,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACvFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,aAAa;IACjCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,MAAM;IACZiD,QAAQ,EAAE,OAAO;IACjBe,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,OAAO;IAC3BjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBf,SAAS,EAAE,aAAa;IACxBD,IAAI,EAAGU,GAAG;MAAA,IAAAuB,eAAA;MAAA,oBACR3I,OAAA;QAAK2G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B,EAAA+B,eAAA,GAAAvB,GAAG,CAACwB,UAAU,cAAAD,eAAA,uBAAdA,eAAA,CAAgBzC,IAAI,CAAC,IAAI,CAAC,KAAI;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;EAEV,CAAC,EACD;IACEd,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,WAAW;IACjBiD,QAAQ,EAAE,WAAW;IACrBJ,QAAQ,EAAGC,GAAG,IAAK;MACjB,MAAMnE,QAAQ,GAAGmE,GAAG,CAACyB,KAAK,IAAIzB,GAAG,CAACyB,KAAK,CAAC,CAAC,CAAC,GAAGzB,GAAG,CAACyB,KAAK,CAAC,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI;MAC1E,OAAO7F,QAAQ,IAAI,KAAK;IAC1B,CAAC;IACDqE,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,SAAS;IACfiD,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAAC2B,OAAO,IAAI,KAAK;IACvCzB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,gBAAgB;IACtBiD,QAAQ,EAAE,kBAAkB;IAC5BJ,QAAQ,EAAGC,GAAG;MAAA,IAAA4B,qBAAA;MAAA,OAAK,EAAAA,qBAAA,GAAA5B,GAAG,CAAC6B,gBAAgB,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsB/D,GAAG,CAACiE,MAAM,IAAIA,MAAM,CAAC5E,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACpFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,kBAAkB;IACtCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,iBAAiB;IACvBiD,QAAQ,EAAE,mBAAmB;IAC7BJ,QAAQ,EAAGC,GAAG;MAAA,IAAA+B,qBAAA;MAAA,OAAK,EAAAA,qBAAA,GAAA/B,GAAG,CAACpC,iBAAiB,cAAAmE,qBAAA,uBAArBA,qBAAA,CAAuBlE,GAAG,CAACiE,MAAM,IAAIA,MAAM,CAAC5E,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACrFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,mBAAmB;IACvCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,eAAe;IACrBiD,QAAQ,EAAE,eAAe;IACzBJ,QAAQ,EAAGC,GAAG;MAAA,IAAAgC,kBAAA;MAAA,OAAK,EAAAA,kBAAA,GAAAhC,GAAG,CAACiC,aAAa,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBnE,GAAG,CAACqE,OAAO,IAAIA,OAAO,CAAChF,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACnFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,eAAe;IACnCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,kBAAkB;IACxBiD,QAAQ,EAAE,oBAAoB;IAC9BJ,QAAQ,EAAGC,GAAG;MAAA,IAAAmC,qBAAA;MAAA,OAAK,EAAAA,qBAAA,GAAAnC,GAAG,CAACoC,kBAAkB,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBtE,GAAG,CAACiE,MAAM,IAAIA,MAAM,CAAC5E,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACtFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,oBAAoB;IACxCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,eAAe;IACrBiD,QAAQ,EAAE,WAAW;IACrBJ,QAAQ,EAAGC,GAAG,IAAK;MAAA,IAAAqC,aAAA;MACjB,MAAMC,SAAS,GAAG,CAAAtC,GAAG,aAAHA,GAAG,wBAAAqC,aAAA,GAAHrC,GAAG,CAAEuC,QAAQ,cAAAF,aAAA,uBAAbA,aAAA,CAAeG,OAAO,CAACC,MAAM;QAAA,IAAAC,iBAAA;QAAA,QAAAA,iBAAA,GAC7CD,MAAM,CAACH,SAAS,cAAAI,iBAAA,uBAAhBA,iBAAA,CAAkB7E,GAAG,CAAC8E,QAAQ,IAAIA,QAAQ,CAACC,cAAc,CAAC;MAAA,CAC5D,CAAC,KAAI,EAAE;MACP,OAAON,SAAS,CAACxD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;IACnC,CAAC;IACDoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC4C,cAAc,IAAI,KAAK;IACvDzB,kBAAkB,EAAE,WAAW;IAC/BjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,eAAe;IACrBiD,QAAQ,EAAE,UAAU;IACpBJ,QAAQ,EAAGC,GAAG;MAAA,IAAA6C,cAAA;MAAA,OAAK,EAAAA,cAAA,GAAA7C,GAAG,CAACuC,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAchF,GAAG,CAAC4E,MAAM,IAAIA,MAAM,CAACvF,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IAC5EoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,UAAU;IAC9BjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,gBAAgB;IACtBiD,QAAQ,EAAE,iBAAiB;IAC3BJ,QAAQ,EAAGC,GAAG;MAAA,IAAA8C,oBAAA;MAAA,OAAK,EAAAA,oBAAA,GAAA9C,GAAG,CAAC+C,eAAe,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBjF,GAAG,CAACiE,MAAM,IAAIA,MAAM,CAAC5E,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACnFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,iBAAiB;IACrCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBjC,IAAI,EAAE,oBAAoB;IAC1BiD,QAAQ,EAAE,iBAAiB;IAC3BJ,QAAQ,EAAGC,GAAG;MAAA,IAAAgD,oBAAA;MAAA,OAAK,EAAAA,oBAAA,GAAAhD,GAAG,CAACiD,eAAe,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBnF,GAAG,CAACiE,MAAM,IAAIA,MAAM,CAAC5E,IAAI,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;IAAA;IACnFoC,iBAAiB,EAAGlB,GAAG,IAAKA,GAAG,CAAC9C,IAAI,IAAI,KAAK;IAC7CiE,kBAAkB,EAAE,iBAAiB;IACrCjB,IAAI,EAAE,KAAK;IACXG,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,CACF,EAAE,CAACxF,WAAW,EAAEF,OAAO,CAAC,CAAC;EAE1B,MAAMsI,cAAc,GAAGhM,OAAO,CAAC,MAAM;IACnC,OAAOkI,UAAU,CAACvB,GAAG,CAACsF,MAAM,IAAI;MAC9B,IAAInK,eAAe,CAACoE,QAAQ,CAAC+F,MAAM,CAACjG,IAAI,CAAC,EAAE;QACzC,OAAO;UAAE,GAAGiG,MAAM;UAAEjD,IAAI,EAAE;QAAM,CAAC;MACnC,CAAC,MAAM;QACL,OAAO;UAAE,GAAGiD,MAAM;UAAEjD,IAAI,EAAE;QAAK,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMgE,0BAA0B,GAAGlM,OAAO,CAAC,MAAM;IAC/C,OAAOgM,cAAc,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAChD,UAAU,KAAK,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAC4C,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAInF,MAAM,CAACoF,IAAI,CAACnK,qBAAqB,CAAC,CAACgD,MAAM,EAAE;MAC7C,IAAIoH,MAAM,GAAG,CAAC,CAAC;MACfrF,MAAM,CAACoF,IAAI,CAACnK,qBAAqB,CAAC,CAACmE,OAAO,CAAEgB,GAAG,IAAK;QAClD,IAAI,OAAOnF,qBAAqB,CAACmF,GAAG,CAAC,KAAK,QAAQ,EAAE;UAClDiF,MAAM,CAACjF,GAAG,CAAC,GAAG,EAAE;QAClB,CAAC,MAAM;UACLiF,MAAM,CAACjF,GAAG,CAAC,GAAG,EAAE;QAClB;MACF,CAAC,CAAC;MACFlF,wBAAwB,CAAC;QAAE,GAAGmK;MAAO,CAAC,CAAC;MACvCxF,gBAAgB,CAAC;QAAE,GAAGwF;MAAO,CAAC,CAAC;IACjC;IACA1I,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAM2I,QAAQ,GAAGhM,WAAW,CAAC,CAAC;EAC9B,MAAMiM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMF,QAAQ,CAC3BxL,WAAW,CAAC2L,SAAS,CAACC,WAAW,CAACC,QAAQ,CAAC;QACzC3I,OAAO,EAAEZ,UAAU;QACnBa,KAAK,EAAEX,aAAa;QACpBY,IAAI,EAAER,WAAW;QACjBS,QAAQ,EAAE,CAAAN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+I,KAAK,KAAI,EAAE;QAChCxI,KAAK,EAAE/B;MACT,CAAC,CACH,CAAC,CAACwK,MAAM,CAAC,CAAC;MAEV,IAAI,EAACL,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEI,KAAK,KAAIJ,MAAM,CAACI,KAAK,GAAG,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MACA,IAAIE,EAAE,GAAG,CAAC;MACV,IAAIC,WAAW,GAAGP,MAAM,CAAC5I,IAAI,CAAC6C,GAAG,CAAEf,IAAI,IAAK;QAC1C,IAAIoG,cAAc,CAAC7G,MAAM,EAAE;UACzB,IAAI+H,GAAG,GAAG,CAAC,CAAC;UACZlB,cAAc,CAAC1F,OAAO,CAAE2F,MAAM,IAAK;YACjC,IAAI,CAACA,MAAM,CAACjD,IAAI,IAAIiD,MAAM,CAACpD,QAAQ,EAAE;cACnCqE,GAAG,CAACjB,MAAM,CAACjG,IAAI,CAAC,GAAGiG,MAAM,CAACjG,IAAI,KAAK,MAAM,GAAGgH,EAAE,EAAE,GAAGf,MAAM,CAACpD,QAAQ,CAACjD,IAAI,CAAC,IAAI,EAAE;YAChF;UACF,CAAC,CAAC;UACF,OAAOsH,GAAG;QACZ;MACF,CAAC,CAAC;MAEF,MAAMC,SAAS,GAAGpM,IAAI,CAACqM,KAAK,CAACC,aAAa,CAACJ,WAAW,CAAC;MACvD,MAAMK,QAAQ,GAAGvM,IAAI,CAACqM,KAAK,CAACG,QAAQ,CAAC,CAAC;MACtCxM,IAAI,CAACqM,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,QAAQ,CAAC;MAE3D,MAAMM,WAAW,GAAG1M,IAAI,CAAC2M,KAAK,CAACJ,QAAQ,EAAE;QACvCK,QAAQ,EAAE,MAAM;QAChB5D,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAM6D,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,WAAW,CAAC,EAAE;QAAE1D,IAAI,EAAE;MAA2B,CAAC,CAAC;MAC1EjJ,MAAM,CAAC8M,IAAI,EAAE,GAAG/L,WAAW,CAACiM,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAIb,WAAW,CAAC9H,MAAM,OAAO,CAAC;IAC9E,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdgL,OAAO,CAAChL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMiL,2BAA2B,GAAGlO,WAAW,CAC7C,OACEmO,UAAU,GAAG,CAAC,CAAC,EACflE,IAAI,GAAG,OAAO,EACdmE,SAAS,GAAG,EAAE,EACdC,SAAS,GAAG,QAAQ,KACjB;IACH,IAAIC,YAAY,GAAGH,UAAU,CAAChF,QAAQ,IAAI,OAAO;IACjD,IAAIgB,kBAAkB,GAAGgE,UAAU,CAAChE,kBAAkB,IAAI,EAAE;IAE5D,IAAI;MACF3H,mBAAmB,CAAC8L,YAAY,CAAC;MACjCxL,sBAAsB,CAAC,IAAI,CAAC;MAC5B,IAAIyL,SAAS,GAAG,EAAE;MAClB,MAAMC,QAAQ,GAAG,MAAM/J,oBAAoB,CAAC;QAC1CwF,IAAI,EAAEA,IAAI,CAAC1D,IAAI,CAAC,CAAC;QACjB4F,MAAM,EAAEmC,YAAY,CAAC/H,IAAI,CAAC,CAAC;QAC3BkI,KAAK,EAAEtE,kBAAkB,CAAC5D,IAAI,CAAC,CAAC;QAChCmI,IAAI,EAAEN,SAAS,CAAC7H,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAIiI,QAAQ,CAACxK,IAAI,EAAE;QACjBuK,SAAS,GAAGC,QAAQ,CAACxK,IAAI;MAC3B;MAEA,IAAIuK,SAAS,CAAClJ,MAAM,EAAE;QACpB,IAAIgJ,SAAS,KAAK,YAAY,EAAE;UAC9BjM,gBAAgB,CAAEuM,IAAI,KAAM;YAC1B,GAAGA,IAAI;YACP,CAACL,YAAY,GAAGC;UAClB,CAAC,CAAC,CAAC;UACH,OAAOA,SAAS;QAClB;QAEA,MAAMK,gBAAgB,GAAGL,SAAS,CAC/B1H,GAAG,CAAEf,IAAI,IAAK;UACb,IAAIqI,UAAU,CAACpF,QAAQ,EAAE;YACvB,IAAI8F,KAAK,GAAG,EAAE;YACd,IAAIpH,KAAK,GAAG3B,IAAI,CAACwI,YAAY,CAAC;YAC9B,IAAIH,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEjE,iBAAiB,EAAE;cACjC2E,KAAK,GAAGV,UAAU,CAACjE,iBAAiB,CAACpE,IAAI,CAAC;cAC1C2B,KAAK,GAAG3B,IAAI,CAACkC,EAAE;YACjB,CAAC,MAAM;cACL6G,KAAK,GAAGV,UAAU,CAACpF,QAAQ,CAACjD,IAAI,CAAC;YACnC;YACA,IAAI+I,KAAK,EAAE;cACT,IAAI/I,IAAI,CAACkH,KAAK,IAAIlH,IAAI,CAACkH,KAAK,GAAG,CAAC,EAAE;gBAChC6B,KAAK,IAAI,KAAK/I,IAAI,CAACkH,KAAK,GAAG;cAC7B;cACA,OAAO;gBAAE6B,KAAK;gBAAEpH;cAAM,CAAC;YACzB;YACA,OAAO,IAAI;UACb;UACA,OAAO,IAAI;QACb,CAAC,CAAC,CACD4E,MAAM,CAACyC,OAAO,CAAC;QAElB,MAAMC,aAAa,GAAGjO,WAAW,CAAC8N,gBAAgB,CAAC;QACnDxM,gBAAgB,CAAEuM,IAAI,KAAM;UAC1B,GAAGA,IAAI;UACP,CAACR,UAAU,CAACnG,EAAE,GAAG+G,aAAa,CAAE;QAClC,CAAC,CAAC,CAAC;QACH,OAAOA,aAAa;MACtB,CAAC,MAAM;QACL;QACA3M,gBAAgB,CAAEuM,IAAI,KAAM;UAC1B,GAAGA,IAAI;UACP,CAACR,UAAU,CAACnG,EAAE,GAAG;QACnB,CAAC,CAAC,CAAC;QACH,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAO/E,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC+L,OAAO,IAAI,gCAAgC,CAAC;MAC3D,OAAO,EAAE;IACX,CAAC,SAAS;MACRlM,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC,EACD,CAAC2B,oBAAoB,CACvB,CAAC;EAED,oBACE7C,OAAA;IAAS2G,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAChF5G,OAAA;MAAK2G,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE5B5G,OAAA;QAAK2G,SAAS,EAAC,gGAAgG;QAAAC,QAAA,gBAC7G5G,OAAA;UAAK2G,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3C5G,OAAA;YAAI2G,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEzG;UAAW;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNlH,OAAA;UAAK2G,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtD5G,OAAA,CAACtB,aAAa;YAAC2O,OAAO,EAAE7G,UAAW;YAAC8G,UAAU,EAAEA,CAAA,KAAM,CAAC;UAAE;YAAAvG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE3D,CAAC5E,UAAU,IAAID,SAAS,IAAIkL,QAAQ,CAAClL,SAAS,CAAC+I,KAAK,CAAC,GAAG,CAAC,iBACxDpL,OAAA,CAAAE,SAAA;YAAA0G,QAAA,eACE5G,OAAA;cACE2G,SAAS,EAAC,mZAAmZ;cAC7ZE,OAAO,EAAEkE,aAAc;cAAAnE,QAAA,GAEtBtE,UAAU,iBACTtC,OAAA,CAAAE,SAAA;gBAAA0G,QAAA,eACE5G,OAAA;kBAAM2G,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAEtE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC,gBACP,CACH,EACA,CAAC5E,UAAU,iBACVtC,OAAA;gBAAM2G,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAEzD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EAAC,mBACe,EAAC7E,SAAS,CAAC+I,KAAK,EAAC,GACpC;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC,gBACT,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAINlH,OAAA,CAACrB,YAAY;QACX0O,OAAO,EAAE7C,0BAA2B,CAAC;QAAA;QACrC/J,qBAAqB,EAAEA,qBAAsB;QAC7CC,wBAAwB,EAAEA,wBAAyB;QACnD4L,2BAA2B,EAAEA,2BAA4B;QACzD/L,aAAa,EAAEA,aAAc;QAC7BU,mBAAmB,EAAEA,mBAAoB;QACzCN,gBAAgB,EAAEA,gBAAiB;QACnCgK,SAAS,EAAEA,SAAU;QACrBxI,cAAc,EAAEA,cAAe;QAC/BkD,gBAAgB,EAAEA;MAAiB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAGD3E,UAAU,iBAAIvC,OAAA;QAAK2G,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,uBAAqB,EAACrE,UAAU,CAAC6K,OAAO,IAAI,eAAe;MAAA;QAAArG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAG9G5E,UAAU,iBAAItC,OAAA,CAACxB,OAAO;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB,CAAC5E,UAAU,IAAID,SAAS,iBACvBrC,OAAA;QAAU2G,SAAS,EAAC,2EAA2E;QAAAC,QAAA,gBAC7F5G,OAAA;UAAQ2G,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAE9D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA;UAAK2G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5G,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAC9C,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACE;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNlH,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAChD,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACG;YAAW;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNlH,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SACjD,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACI;YAAY;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNlH,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SACnD,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACK;YAAc;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNlH,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SACpD,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACM;YAAe;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNlH,OAAA;YAAK2G,SAAS,EAAEhF,gBAAiB;YAAAiF,QAAA,gBAC/B5G,OAAA;cAAM2G,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAC7C,eAAAlH,OAAA;cAAA4G,QAAA,EAAO7D,WAAW,CAACO;YAAO;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACX,eAGDlH,OAAA;QAAK2G,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D5G,OAAA,CAACzB,SAAS;UACR8O,OAAO,EAAE/C,cAAe;UACxBlI,IAAI,EAAE,CAAAC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAG;UAC5BuE,SAAS,EAAC,6BAA6B;UACvC6G,WAAW;UACXC,gBAAgB;UAChBC,UAAU;UACVC,UAAU;UACVC,gBAAgB;UAChBC,iBAAiB,EAAE7L,OAAQ;UAC3B8L,mBAAmB,EAAE,CAAAzL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+I,KAAK,KAAI,CAAE;UAC3C2C,YAAY,EAAGrL,IAAI,IAAK;YACtB,IAAIA,IAAI,KAAKR,WAAW,EAAE;cACxBC,cAAc,CAACO,IAAI,CAAC;YACtB;UACF,CAAE;UACFsL,mBAAmB,EAAGC,UAAU,IAAK;YACnC,IAAIA,UAAU,KAAKjM,OAAO,EAAE;cAC1BC,UAAU,CAACgM,UAAU,CAAC;cACtB9L,cAAc,CAAC,CAAC,CAAC;YACnB;UACF,CAAE;UACF+L,0BAA0B,EAAE;YAC1BC,iBAAiB,EAAE,IAAI;YACvBC,qBAAqB,EAAE;UACzB,CAAE;UACFC,UAAU;UACVC,MAAM,EAAEA,CAAC/D,MAAM,EAAEgE,kBAAkB,GAAG,MAAM,KAAK;YAC/C,IAAI/I,MAAM,CAACoF,IAAI,CAACL,MAAM,CAAC,CAAC9G,MAAM,EAAE;cAC9B5B,aAAa,CAAC0I,MAAM,CAAChD,QAAQ,IAAIgD,MAAM,CAACjG,IAAI,IAAI,YAAY,CAAC;cAC7DvC,gBAAgB,CAACwM,kBAAkB,IAAI,MAAM,CAAC;YAChD;UACF;QAAE;UAAAxH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL3F,QAAQ,iBACPvB,OAAA,CAACpB,SAAS;QAACsF,IAAI,EAAE3C,QAAS;QAACC,WAAW,EAAEA,WAAY;QAAC6L,OAAO,EAAE/C;MAAe;QAAAvD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAChF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC5G,EAAA,CAjqBID,oBAAoB;EAAA,QAWPX,WAAW,EACAI,kBAAkB,EASaN,mBAAmB,EAQ/CC,uCAAuC,EAC7CF,yBAAyB,EA6XjCT,WAAW;AAAA;AAAA0P,EAAA,GA3ZxBnO,oBAAoB;AAmqB1B,eAAeA,oBAAoB;AAAC,IAAAmO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}