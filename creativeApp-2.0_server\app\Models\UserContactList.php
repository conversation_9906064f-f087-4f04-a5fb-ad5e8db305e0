<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserContactList extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'frequent_contact_id'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function contact()
    {
        return $this->belongsTo(User::class, 'frequent_contact_id');
    }
}
