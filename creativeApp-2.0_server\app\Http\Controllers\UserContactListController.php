<?php

namespace App\Http\Controllers;

use App\Models\UserContactList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class UserContactListController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $userId = $request->userId;

        $frequentContacts = UserContactList::with('contact') // Eager load the contact's details
            ->where('user_id', $userId)
            ->get();

        Log::info('All Contacts Retrieved:', ['contact_count' => $frequentContacts->count()]);

        return response()->json(['contacts' => $frequentContacts], 200);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Validate the request data
        $request->validate([
            'user_id' => 'required|integer|max:20',
            'frequent_contact_id' => 'required|integer|max:20'
        ]);

        // Log the request data
        Log::info('Create Contact Request:', ['request' => $request->all()]);

        // Check if the role name already exists
        if (UserContactList::where('frequent_contact_id', $request->frequent_contact_id)->exists()) {
            return response()->json(['error' => 'Contact already Added.'], 409);
        }

        // Add frequent contact
        $contact = UserContactList::create([
            'user_id' => $authUser->id,
            'frequent_contact_id' => $request->frequent_contact_id,
        ]);

        Log::info('Frequent contact added:', ['contact' => $contact]);

        return response()->json(['message' => 'Frequent contact added successfully', 'contact' => $contact], 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserContactList  $userContactList
     * @return \Illuminate\Http\Response
     */
    public function show(UserContactList $userContactList)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserContactList  $userContactList
     * @return \Illuminate\Http\Response
     */
    public function edit(UserContactList $userContactList)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserContactList  $userContactList
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UserContactList $userContactList)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserContactList  $userContactList
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser) {
            // Find the contact
            $contact = UserContactList::findOrFail($request->id);
            // Delete the contact
            $contact->delete();

            return response()->json(['message' => 'Contact removed successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this role.'], 403);
    }

   public function toggle(Request $request)
{
    $userId = Auth::id();
    $contactId = $request->input('contact_user_id');

    $existing = UserContactList::where('user_id', $userId)
        ->where('frequent_contact_id', $contactId)
        ->first();

    if ($existing) {
        $existing->delete();
        return response()->json(['message' => 'Contact removed from frequent list']);
    } else {
        UserContactList::create([
            'user_id' => $userId,
            'frequent_contact_id' => $contactId
        ]);
        return response()->json(['message' => 'Contact added to frequent list']);
    }
}
}
