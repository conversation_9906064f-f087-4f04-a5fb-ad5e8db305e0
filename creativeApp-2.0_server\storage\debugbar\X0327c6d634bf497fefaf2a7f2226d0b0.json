{"__meta": {"id": "X0327c6d634bf497fefaf2a7f2226d0b0", "datetime": "2025-08-18 22:12:48", "utime": 1755533568.426026, "method": "PUT", "uri": "/api/teams/43", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 14, "messages": [{"message": "[22:12:48] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.406729, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Authenticated User: {\n    \"user_id\": 150,\n    \"fname\": \"<PERSON><PERSON>\",\n    \"lname\": \"Shaikh\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.408289, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Request Files: {\n    \"files\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.408374, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Name from input: {\n    \"name\": \"Clipcentric\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.408452, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Icon from input: {\n    \"icon\": \"data:image\\/png;base64,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\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.408842, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Logo from input: {\n    \"logo\": \"data:image\\/png;base64,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\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409085, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: POC from input: {\n    \"poc\": \"Saiful Islam\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409199, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Manager from input: {\n    \"manager\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>l <PERSON>\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409279, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Team Lead from input: {\n    \"team_lead\": \"<PERSON><PERSON>\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409348, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Launch from input: {\n    \"launch\": \"2025-08-18\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409412, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Department ID from input: {\n    \"department_id\": 8\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.409474, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Decoded Logo Path: {\n    \"logo_path\": \"images\\/logo-68a3510063ff3.png\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.410345, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.info: Decoded Icon Path: {\n    \"icon_path\": \"images\\/icon-68a35100641ce.png\"\n}", "message_html": null, "is_string": false, "label": "info", "time": 1755533568.410433, "xdebug_link": null, "collector": "log"}, {"message": "[22:12:48] LOG.error: Team Update Validation Error: {\n    \"errors\": {\n        \"name\": [\n            \"The name has already been taken.\"\n        ]\n    },\n    \"team_id\": \"43\"\n}", "message_html": null, "is_string": false, "label": "error", "time": 1755533568.421197, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755533567.849683, "end": 1755533568.426043, "duration": 0.5763599872589111, "duration_str": "576ms", "measures": [{"label": "Booting", "start": 1755533567.849683, "relative_start": 0, "end": 1755533568.329789, "relative_end": 1755533568.329789, "duration": 0.4801058769226074, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755533568.329801, "relative_start": 0.4801180362701416, "end": 1755533568.426044, "relative_end": 9.5367431640625e-07, "duration": 0.09624290466308594, "duration_str": "96.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24340432, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT api/teams/{id}", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "controller": "App\\Http\\Controllers\\TeamController@updateTeam", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=330\" onclick=\"\">app/Http/Controllers/TeamController.php:330-452</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023980000000000005, "accumulated_duration_str": "23.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": 1755533568.363142, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '444' limit 1", "type": "query", "params": [], "bindings": ["444"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": 1755533568.369173, "duration": 0.019850000000000003, "duration_str": "19.85ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 82.777}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": 1755533568.393665, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 82.777, "width_percent": 1.418}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-18 22:12:48', `personal_access_tokens`.`updated_at` = '2025-08-18 22:12:48' where `id` = 444", "type": "query", "params": [], "bindings": ["2025-08-18 22:12:48", "2025-08-18 22:12:48", 444], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": 1755533568.395947, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 84.195, "width_percent": 11.093}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": 1755533568.404962, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 95.288, "width_percent": 1.626}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": 1755533568.406935, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 96.914, "width_percent": 1.084}, {"sql": "select count(*) as aggregate from `teams` where `name` = 'Clipcentric' and `id` <> '43'", "type": "query", "params": [], "bindings": ["Clipcentric", "43"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 928}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 453}], "start": 1755533568.417062, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 97.998, "width_percent": 0.959}, {"sql": "select count(*) as aggregate from `departments` where `id` = 8", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": 1755533568.4196708, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 98.957, "width_percent": 1.043}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/teams/43", "status_code": "<pre class=sf-dump id=sf-dump-1091092788 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-1091092788\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1828009533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828009533\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1726185711 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n  \"<span class=sf-dump-key>department_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n  \"<span class=sf-dump-key>workday</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Tuesday</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">Wednesday</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Thursday</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">Monday</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>billable_hours</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Saiful Islam</span>\"\n  \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n  \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Rajib Shaikh</span>\"\n  \"<span class=sf-dump-key>updated_by</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"39858 characters\">data:image/png;base64,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</span>\"\n  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"8022 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726185711\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-875199745 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">48132</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 444|e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875199745\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1672439532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1672439532\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1614916455 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 16:12:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">148</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614916455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-244996981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-244996981\", {\"maxDepth\":0})</script>\n"}}