{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\EditTeam.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { alertMessage } from \"../../common/coreui\";\nimport Select from \"react-select\";\nimport { useDispatch } from 'react-redux';\nimport { teamApi } from './../../features/api';\nimport { X, Users, Building, User, Calendar, Clock, Upload, AlertCircle, CheckCircle } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst isTokenValid = () => {\n  const token = localStorage.getItem(\"token\");\n  return token !== null;\n};\nconst EditTeam = ({\n  dataItemsId,\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [team, setTeam] = useState(null);\n  const [teamName, setTeamName] = useState(\"\");\n  const [icon, setIcon] = useState(null);\n  const [existingIcon, setExistingIcon] = useState(\"\");\n  const [logo, setLogo] = useState(null);\n  const [existingLogo, setExistingLogo] = useState(\"\");\n  const [poc, setPoc] = useState(\"\");\n  const [manager, setManager] = useState(\"\");\n  const [teamLead, setTeamLead] = useState(\"\");\n  const [workday, setWorkday] = useState([]);\n  const [billableHours, setBillableHours] = useState(\"\");\n  const [launch, setLaunch] = useState(\"\");\n  const [departmentId, setDepartmentId] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [focusedField, setFocusedField] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(\"\");\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // React Select states\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\n  const [selectedPoc, setSelectedPoc] = useState(null);\n  const [selectedManager, setSelectedManager] = useState(null);\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\n\n  // Create options for React Select dropdowns\n  const departmentOptions = departments.map(dept => ({\n    value: dept.id,\n    label: dept.name\n  }));\n\n  // Filter based on Responsibility Level (resource_types) instead of roles\n  const pocOptions = users.filter(user => {\n    var _user$resource_types;\n    const hasValidResponsibility = (_user$resource_types = user.resource_types) === null || _user$resource_types === void 0 ? void 0 : _user$resource_types.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n  const managerOptions = users.filter(user => {\n    var _user$resource_types2;\n    const hasValidResponsibility = (_user$resource_types2 = user.resource_types) === null || _user$resource_types2 === void 0 ? void 0 : _user$resource_types2.some(rt => ['Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n  const teamLeadOptions = users.filter(user => {\n    var _user$resource_types3;\n    const hasValidResponsibility = (_user$resource_types3 = user.resource_types) === null || _user$resource_types3 === void 0 ? void 0 : _user$resource_types3.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\n    const hasValidName = user.fullName && user.fullName.trim() !== '';\n    return hasValidResponsibility && hasValidName;\n  }).map(user => ({\n    value: user.fullName,\n    label: user.fullName\n  }));\n\n  // Debug logs for all options\n  console.log('EditTeam - Total users:', users.length);\n  console.log('EditTeam - Manager options:', managerOptions.length);\n  console.log('EditTeam - POC options:', pocOptions.length);\n  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);\n\n  // Days of the week for multi-select\n  const daysOfWeek = [{\n    value: 'Monday',\n    label: 'Monday'\n  }, {\n    value: 'Tuesday',\n    label: 'Tuesday'\n  }, {\n    value: 'Wednesday',\n    label: 'Wednesday'\n  }, {\n    value: 'Thursday',\n    label: 'Thursday'\n  }, {\n    value: 'Friday',\n    label: 'Friday'\n  }, {\n    value: 'Saturday',\n    label: 'Saturday'\n  }, {\n    value: 'Sunday',\n    label: 'Sunday'\n  }];\n\n  // Handle workday selection\n  const handleWorkdayChange = selectedOptions => {\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\n    setWorkday(selectedValues);\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!isTokenValid()) {\n        setError(\"No authentication token found.\");\n        setLoading(false);\n        return;\n      }\n      const token = localStorage.getItem(\"token\");\n      try {\n        // Fetch Users\n        const usersResponse = await fetch(`${API_URL}/users`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!usersResponse.ok) {\n          throw new Error(\"Failed to fetch users\");\n        }\n        const usersData = await usersResponse.json();\n        // console.log('EditTeam Users data:', usersData);\n        setUsers(usersData.map(user => ({\n          id: user.id,\n          fullName: `${(user.fname || \"\").trim()} ${(user.lname || \"\").trim()}`.trim(),\n          fname: user.fname,\n          lname: user.lname,\n          roles: Array.isArray(user.roles) ? user.roles.map(r => (r.name || \"\").trim()) : [],\n          resource_types: Array.isArray(user.resource_types) ? user.resource_types.map(rt => (rt.name || \"\").trim()) : []\n        })));\n\n        // Fetch Departments\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!departmentsResponse.ok) {\n          throw new Error(\"Failed to fetch departments\");\n        }\n        const departmentsData = await departmentsResponse.json();\n        setDepartments(departmentsData.departments);\n\n        // Fetch Team Details if editing an existing team\n        if (dataItemsId) {\n          const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          if (!teamResponse.ok) {\n            throw new Error(\"Failed to fetch team details\");\n          }\n          const teamData = await teamResponse.json();\n\n          // Set team data to state\n          setTeam(teamData.team);\n\n          // Set values for form fields based on teamData\n          setTeamName(teamData.team.name || \"\");\n          setIcon(teamData.team.icon || null);\n          setLogo(teamData.team.logo || null);\n          setPoc(teamData.team.poc || \"\");\n          setManager(teamData.team.manager || \"\");\n          setTeamLead(teamData.team.team_lead || \"\");\n          // Handle workday - it comes as JSON string from backend\n          let workdayArray = [];\n          if (teamData.team.workday) {\n            try {\n              workdayArray = typeof teamData.team.workday === 'string' ? JSON.parse(teamData.team.workday) : teamData.team.workday;\n            } catch (e) {\n              console.error('Error parsing workday:', e);\n              workdayArray = [];\n            }\n          }\n          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);\n          setBillableHours(teamData.team.billable_hours || \"\");\n          setLaunch(teamData.team.launch || \"\");\n          setExistingIcon(teamData.team.icon || \"\");\n          setExistingLogo(teamData.team.logo || \"\");\n\n          // Set departmentId by accessing the first department's ID if available\n          const departmentId = teamData.team.departments && teamData.team.departments.length > 0 ? teamData.team.departments[0].id : \"\";\n          setDepartmentId(departmentId);\n\n          // Set React Select default values after data is loaded\n          setTimeout(() => {\n            if (departmentId) {\n              const deptOption = departmentsData.departments.find(d => d.id === departmentId);\n              if (deptOption) {\n                setSelectedDepartment({\n                  value: deptOption.id,\n                  label: deptOption.name\n                });\n              }\n            }\n            if (teamData.team.poc) {\n              setSelectedPoc({\n                value: teamData.team.poc,\n                label: teamData.team.poc\n              });\n            }\n            if (teamData.team.manager) {\n              setSelectedManager({\n                value: teamData.team.manager,\n                label: teamData.team.manager\n              });\n            }\n            if (teamData.team.team_lead) {\n              setSelectedTeamLead({\n                value: teamData.team.team_lead,\n                label: teamData.team.team_lead\n              });\n            }\n          }, 100);\n        }\n      } catch (error) {\n        setError(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [dataItemsId]);\n\n  // Fetch logged-in user data (user_id)\n  useEffect(() => {\n    const userId = localStorage.getItem(\"user_id\");\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setError(\"\"); // Clear any previous error\n\n    // Get user_id from localStorage for 'updated_by'\n    const updatedBy = loggedInUser;\n    if (!updatedBy) {\n      setError('User is not logged in.');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setError('Authentication token is missing.');\n        return;\n      }\n\n      // Fetch the logged-in user's data for full name\n      const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!loggedUserResponse.ok) {\n        throw new Error('Failed to fetch logged-in user data');\n      }\n      const loggedUserData = await loggedUserResponse.json();\n      const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`;\n      // console.log('Logged-in User Full Name:', fullName); \n\n      // Prepare the data object (without file data)\n      const requestData = {\n        name: teamName.trim(),\n        department_id: parseInt(departmentId),\n        launch: launch,\n        workday: workday,\n        billable_hours: billableHours ? parseInt(billableHours) : null,\n        poc: poc,\n        manager: manager,\n        team_lead: teamLead,\n        updated_by: updatedBy\n      };\n\n      // Convert icon and logo files to Base64 if they are provided\n      if (icon && icon instanceof File) {\n        // Check if it's a valid File\n        const iconBase64 = await convertToBase64(icon);\n        requestData.icon = iconBase64;\n      } else {\n        console.log(\"No valid icon file selected.\");\n      }\n      if (logo && logo instanceof File) {\n        // Check if it's a valid File\n        const logoBase64 = await convertToBase64(logo);\n        requestData.logo = logoBase64;\n      } else {\n        console.log(\"No valid logo file selected.\");\n      }\n\n      // Log the final request data before submission\n      console.log(\"Request data before submission:\", requestData);\n\n      // Make the PUT request to update the team with JSON data\n      const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestData) // Send data as JSON\n      });\n      if (!response.ok) {\n        throw new Error('Failed to update team: ' + response.statusText);\n      }\n      const result = await response.json();\n      //setSuccessMessage(`Team \"${result.name || teamName}\" updated successfully!`);\n      alertMessage('success');\n\n      // Invalidate the 'Team' tag to trigger a refetch of the team list\n      dispatch(teamApi.util.invalidateTags(['Team']));\n\n      // Optionally, close the modal after success\n      setTimeout(() => {\n        setVisible(false);\n        setSuccessMessage('');\n      }, 2000);\n      navigate('/settings');\n    } catch (error) {\n      alertMessage('error');\n    }\n  };\n\n  // Helper function to convert a file to Base64\n  const convertToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      if (file instanceof File) {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(file);\n      } else {\n        reject('The provided object is not a valid File.');\n      }\n    });\n  };\n  const handleClose = () => {\n    setVisible(false);\n    navigate(\"/settings\");\n  };\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-1.5 sm:p-2 bg-white/20 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Users, {\n                className: \"w-4 h-4 sm:w-6 sm:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg sm:text-xl font-semibold\",\n                  children: \"Edit Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white-100 text-xs sm:text-sm\",\n                  children: \"Update team information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\",\n        children: [formError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-left font-medium text-red-800 text-sm sm:text-base\",\n              children: \"Action Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 text-xs sm:text-sm\",\n              children: formError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4 sm:space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Building, {\n                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                    children: \"Organization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 sm:space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 36\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select, {\n                      options: departmentOptions,\n                      value: selectedDepartment,\n                      onChange: option => {\n                        setSelectedDepartment(option);\n                        setDepartmentId((option === null || option === void 0 ? void 0 : option.value) || '');\n                      },\n                      placeholder: \"Select Department\",\n                      className: \"w-full\",\n                      isSearchable: true,\n                      styles: {\n                        control: (base, state) => ({\n                          ...base,\n                          borderRadius: '0.5rem',\n                          borderWidth: '2px',\n                          borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\n                          boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\n                          '&:hover': {\n                            borderColor: '#D1D5DB'\n                          }\n                        })\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                      children: [\"Team Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-500\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: teamName,\n                      onChange: e => setTeamName(e.target.value),\n                      onFocus: () => setFocusedField(\"teamName\"),\n                      onBlur: () => setFocusedField(null),\n                      placeholder: \"Enter team name\",\n                      className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${focusedField === \"teamName\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"poc\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"POC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: pocOptions,\n                value: selectedPoc,\n                onChange: option => {\n                  setSelectedPoc(option);\n                  setPoc((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select POC\",\n                className: \"w-full\",\n                isSearchable: true,\n                noOptionsMessage: () => \"No options available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"manager\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Manager\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: managerOptions,\n                value: selectedManager,\n                onChange: option => {\n                  setSelectedManager(option);\n                  setManager((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select Manager\",\n                className: \"w-full\",\n                isSearchable: true,\n                noOptionsMessage: () => \"No managers found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"teamLead\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Team Lead\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: teamLeadOptions,\n                value: selectedTeamLead,\n                onChange: option => {\n                  setSelectedTeamLead(option);\n                  setTeamLead((option === null || option === void 0 ? void 0 : option.value) || '');\n                },\n                placeholder: \"Select Team Lead\",\n                className: \"w-full\",\n                isSearchable: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Work Days *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                isMulti: true,\n                options: daysOfWeek,\n                value: workday.map(day => ({\n                  value: day,\n                  label: day\n                })),\n                onChange: handleWorkdayChange,\n                placeholder: \"Select Work Days\",\n                className: \"w-full\",\n                isDisabled: loading,\n                noOptionsMessage: () => \"No options available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this), workday.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-2\",\n                children: [\"Selected: \", workday.join(\", \")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"billableHours\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Billable Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"billableHours\",\n                value: billableHours,\n                onChange: e => setBillableHours(e.target.value),\n                min: \"0\",\n                placeholder: \"Enter billable hours\",\n                className: \"w-full p-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"launch\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Launch Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                id: \"launch\",\n                value: launch,\n                onChange: e => setLaunch(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4 opacity-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"icon\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this), icon && icon instanceof File ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(icon),\n                  alt: \"Icon Preview\",\n                  className: \"w-20 h-20 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"icon\",\n                  onChange: e => setIcon(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [(existingIcon || icon) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    // Prefer stored path when string\n                    src: typeof icon === \"string\" && icon ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}` : existingIcon ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}` : \"\",\n                    alt: \"Icon Preview\",\n                    className: \"w-auto h-auto object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"icon\",\n                  onChange: e => setIcon(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full sm:w-1/2 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this), logo && logo instanceof File ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(logo),\n                  alt: \"Logo Preview\",\n                  className: \"w-20 h-20 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"logo\",\n                  onChange: e => setLogo(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [(existingLogo || logo) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    // Prefer stored path when string\n                    src: typeof logo === \"string\" && logo ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}` : existingLogo ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}` : \"\",\n                    alt: \"Logo Preview\",\n                    className: \"w-auto h-auto object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"logo\",\n                  onChange: e => setLogo(e.target.files[0]),\n                  className: \"w-full p-2 border border-gray-300 rounded-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left pt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"material-symbols-rounded text-white text-xl font-regular\",\n                children: \"add_circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 19\n              }, this), loading ? \"Updating...\" : \"Update Team\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 401,\n    columnNumber: 5\n  }, this);\n};\n_s(EditTeam, \"m4GSPGqqjAJsLmoMrddRWHaxN04=\", false, function () {\n  return [useDispatch, useNavigate];\n});\n_c = EditTeam;\nexport default EditTeam;\nvar _c;\n$RefreshReg$(_c, \"EditTeam\");", "map": {"version": 3, "names": ["useEffect", "useState", "useNavigate", "alertMessage", "Select", "useDispatch", "teamApi", "X", "Users", "Building", "User", "Calendar", "Clock", "Upload", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "EditTeam", "dataItemsId", "isVisible", "setVisible", "_s", "dispatch", "navigate", "users", "setUsers", "departments", "setDepartments", "team", "setTeam", "teamName", "setTeamName", "icon", "setIcon", "existingIcon", "setExistingIcon", "logo", "set<PERSON><PERSON>", "existingLogo", "setExistingLogo", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "workday", "setWorkday", "billableHours", "setBillableHours", "launch", "setLaunch", "departmentId", "setDepartmentId", "error", "setError", "errors", "setErrors", "formError", "setFormError", "focusedField", "setFocusedField", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "selectedDepartment", "setSelectedDepartment", "selectedPoc", "setSelectedPoc", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedManager", "selectedTeamLead", "setSelectedTeamLead", "departmentOptions", "map", "dept", "value", "id", "label", "name", "pocOptions", "filter", "user", "_user$resource_types", "hasValidResponsibility", "resource_types", "some", "rt", "includes", "hasValidName", "fullName", "trim", "managerOptions", "_user$resource_types2", "teamLeadOptions", "_user$resource_types3", "console", "log", "length", "daysOfWeek", "handleWorkdayChange", "selectedOptions", "<PERSON><PERSON><PERSON><PERSON>", "option", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "fname", "lname", "roles", "Array", "isArray", "r", "departmentsResponse", "departmentsData", "teamResponse", "teamData", "team_lead", "workdayArray", "JSON", "parse", "e", "billable_hours", "setTimeout", "deptOption", "find", "d", "message", "userId", "handleSubmit", "event", "preventDefault", "updatedBy", "loggedUserResponse", "loggedUserData", "requestData", "department_id", "parseInt", "updated_by", "File", "iconBase64", "convertToBase64", "logoBase64", "response", "body", "stringify", "statusText", "result", "util", "invalidateTags", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onloadend", "onerror", "readAsDataURL", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "options", "onChange", "placeholder", "isSearchable", "styles", "control", "base", "state", "borderRadius", "borderWidth", "borderColor", "isFocused", "boxShadow", "type", "target", "onFocus", "onBlur", "htmlFor", "noOptionsMessage", "is<PERSON><PERSON><PERSON>", "day", "isDisabled", "join", "min", "src", "URL", "createObjectURL", "alt", "files", "REACT_APP_BASE_STORAGE_URL", "class", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/pages/team/EditTeam.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { alertMessage } from \"../../common/coreui\";\r\nimport Select from \"react-select\";\r\nimport { useDispatch } from 'react-redux';\r\nimport { teamApi } from './../../features/api';\r\nimport {\r\n  X,\r\n  Users,\r\n  Building,\r\n  User,\r\n  Calendar,\r\n  Clock,\r\n  Upload,\r\n  AlertCircle,\r\n  CheckCircle,\r\n} from \"lucide-react\";\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem(\"token\");\r\n  return token !== null;\r\n};\r\n\r\nconst EditTeam = ({ dataItemsId, isVisible, setVisible }) => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [departments, setDepartments] = useState([]);\r\n  const [team, setTeam] = useState(null);\r\n  const [teamName, setTeamName] = useState(\"\");\r\n  const [icon, setIcon] = useState(null);\r\n  const [existingIcon, setExistingIcon] = useState(\"\");\r\n  const [logo, setLogo] = useState(null);\r\n  const [existingLogo, setExistingLogo] = useState(\"\");\r\n  const [poc, setPoc] = useState(\"\");\r\n  const [manager, setManager] = useState(\"\");\r\n  const [teamLead, setTeamLead] = useState(\"\");\r\n  const [workday, setWorkday] = useState([]);\r\n  const [billableHours, setBillableHours] = useState(\"\");\r\n  const [launch, setLaunch] = useState(\"\");\r\n  const [departmentId, setDepartmentId] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [errors, setErrors] = useState({});\r\n  const [formError, setFormError] = useState(\"\");\r\n  const [focusedField, setFocusedField] = useState(null);\r\n  const [successMessage, setSuccessMessage] = useState(\"\");\r\n  const [loggedInUser, setLoggedInUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // React Select states\r\n  const [selectedDepartment, setSelectedDepartment] = useState(null);\r\n  const [selectedPoc, setSelectedPoc] = useState(null);\r\n  const [selectedManager, setSelectedManager] = useState(null);\r\n  const [selectedTeamLead, setSelectedTeamLead] = useState(null);\r\n\r\n  // Create options for React Select dropdowns\r\n  const departmentOptions = departments.map(dept => ({\r\n    value: dept.id,\r\n    label: dept.name\r\n  }));\r\n\r\n  // Filter based on Responsibility Level (resource_types) instead of roles\r\n  const pocOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  const managerOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  const teamLeadOptions = users\r\n    .filter(user => {\r\n      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt.name || rt));\r\n      const hasValidName = user.fullName && user.fullName.trim() !== '';\r\n      return hasValidResponsibility && hasValidName;\r\n    })\r\n    .map(user => ({\r\n      value: user.fullName,\r\n      label: user.fullName\r\n    }));\r\n\r\n  // Debug logs for all options\r\n  console.log('EditTeam - Total users:', users.length);\r\n  console.log('EditTeam - Manager options:', managerOptions.length);\r\n  console.log('EditTeam - POC options:', pocOptions.length);\r\n  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);\r\n\r\n\r\n  // Days of the week for multi-select\r\n  const daysOfWeek = [\r\n    { value: 'Monday', label: 'Monday' },\r\n    { value: 'Tuesday', label: 'Tuesday' },\r\n    { value: 'Wednesday', label: 'Wednesday' },\r\n    { value: 'Thursday', label: 'Thursday' },\r\n    { value: 'Friday', label: 'Friday' },\r\n    { value: 'Saturday', label: 'Saturday' },\r\n    { value: 'Sunday', label: 'Sunday' }\r\n  ];\r\n\r\n  // Handle workday selection\r\n  const handleWorkdayChange = (selectedOptions) => {\r\n    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];\r\n    setWorkday(selectedValues);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!isTokenValid()) {\r\n        setError(\"No authentication token found.\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      const token = localStorage.getItem(\"token\");\r\n\r\n            try {\r\n                // Fetch Users\r\n                const usersResponse = await fetch(`${API_URL}/users`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n        if (!usersResponse.ok) {\r\n          throw new Error(\"Failed to fetch users\");\r\n        }\r\n\r\n        const usersData = await usersResponse.json();\r\n        // console.log('EditTeam Users data:', usersData);\r\n        setUsers(\r\n          usersData.map((user) => ({\r\n            id: user.id,\r\n            fullName: `${(user.fname || \"\").trim()} ${(\r\n              user.lname || \"\"\r\n            ).trim()}`.trim(),\r\n            fname: user.fname,\r\n            lname: user.lname,\r\n            roles: Array.isArray(user.roles)\r\n              ? user.roles.map((r) => (r.name || \"\").trim())\r\n              : [],\r\n            resource_types: Array.isArray(user.resource_types)\r\n              ? user.resource_types.map((rt) => (rt.name || \"\").trim())\r\n              : [],\r\n          }))\r\n        );\r\n\r\n                // Fetch Departments\r\n                const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n        if (!departmentsResponse.ok) {\r\n          throw new Error(\"Failed to fetch departments\");\r\n        }\r\n\r\n        const departmentsData = await departmentsResponse.json();\r\n        setDepartments(departmentsData.departments);\r\n\r\n                // Fetch Team Details if editing an existing team\r\n                if (dataItemsId) {\r\n                    const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Authorization': `Bearer ${token}`,\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                    });\r\n\r\n          if (!teamResponse.ok) {\r\n            throw new Error(\"Failed to fetch team details\");\r\n          }\r\n\r\n          const teamData = await teamResponse.json();\r\n\r\n          // Set team data to state\r\n          setTeam(teamData.team);\r\n\r\n          // Set values for form fields based on teamData\r\n          setTeamName(teamData.team.name || \"\"); \r\n          setIcon(teamData.team.icon || null); \r\n          setLogo(teamData.team.logo || null); \r\n          setPoc(teamData.team.poc || \"\"); \r\n          setManager(teamData.team.manager || \"\"); \r\n          setTeamLead(teamData.team.team_lead || \"\"); \r\n          // Handle workday - it comes as JSON string from backend\r\n          let workdayArray = [];\r\n          if (teamData.team.workday) {\r\n            try {\r\n              workdayArray = typeof teamData.team.workday === 'string'\r\n                ? JSON.parse(teamData.team.workday)\r\n                : teamData.team.workday;\r\n            } catch (e) {\r\n              console.error('Error parsing workday:', e);\r\n              workdayArray = [];\r\n            }\r\n          }\r\n          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);\r\n          setBillableHours(teamData.team.billable_hours || \"\"); \r\n          setLaunch(teamData.team.launch || \"\"); \r\n          setExistingIcon(teamData.team.icon || \"\");\r\n          setExistingLogo(teamData.team.logo || \"\");\r\n\r\n          // Set departmentId by accessing the first department's ID if available\r\n          const departmentId =\r\n            teamData.team.departments && teamData.team.departments.length > 0\r\n              ? teamData.team.departments[0].id\r\n              : \"\";\r\n          setDepartmentId(departmentId);\r\n\r\n          // Set React Select default values after data is loaded\r\n          setTimeout(() => {\r\n            if (departmentId) {\r\n              const deptOption = departmentsData.departments.find(d => d.id === departmentId);\r\n              if (deptOption) {\r\n                setSelectedDepartment({ value: deptOption.id, label: deptOption.name });\r\n              }\r\n            }\r\n\r\n            if (teamData.team.poc) {\r\n              setSelectedPoc({ value: teamData.team.poc, label: teamData.team.poc });\r\n            }\r\n\r\n            if (teamData.team.manager) {\r\n              setSelectedManager({ value: teamData.team.manager, label: teamData.team.manager });\r\n            }\r\n\r\n            if (teamData.team.team_lead) {\r\n              setSelectedTeamLead({ value: teamData.team.team_lead, label: teamData.team.team_lead });\r\n            }\r\n          }, 100); \r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [dataItemsId]);\r\n\r\n  // Fetch logged-in user data (user_id)\r\n  useEffect(() => {\r\n    const userId = localStorage.getItem(\"user_id\");\r\n    if (userId) {\r\n      setLoggedInUser(userId);\r\n    }\r\n  }, []);\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n    setError(\"\"); // Clear any previous error\r\n\r\n    // Get user_id from localStorage for 'updated_by'\r\n    const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return;\r\n            }\r\n    \r\n            // Fetch the logged-in user's data for full name\r\n            const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!loggedUserResponse.ok) {\r\n                throw new Error('Failed to fetch logged-in user data');\r\n            }\r\n    \r\n            const loggedUserData = await loggedUserResponse.json();\r\n            const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`; \r\n            // console.log('Logged-in User Full Name:', fullName); \r\n    \r\n            // Prepare the data object (without file data)\r\n            const requestData = {\r\n                name: teamName.trim(),\r\n                department_id: parseInt(departmentId),\r\n                launch: launch,\r\n                workday: workday,\r\n                billable_hours: billableHours ? parseInt(billableHours) : null,\r\n                poc: poc,\r\n                manager: manager,\r\n                team_lead: teamLead,\r\n                updated_by: updatedBy,\r\n            };\r\n    \r\n            // Convert icon and logo files to Base64 if they are provided\r\n            if (icon && icon instanceof File) { // Check if it's a valid File\r\n                const iconBase64 = await convertToBase64(icon);\r\n                requestData.icon = iconBase64;\r\n            } else {\r\n                console.log(\"No valid icon file selected.\");\r\n            }\r\n    \r\n            if (logo && logo instanceof File) { // Check if it's a valid File\r\n                const logoBase64 = await convertToBase64(logo);\r\n                requestData.logo = logoBase64;\r\n            } else {\r\n                console.log(\"No valid logo file selected.\");\r\n            }\r\n    \r\n            // Log the final request data before submission\r\n            console.log(\"Request data before submission:\", requestData);\r\n    \r\n            // Make the PUT request to update the team with JSON data\r\n            const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify(requestData), // Send data as JSON\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update team: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Team \"${result.name || teamName}\" updated successfully!`);\r\n            alertMessage('success');\r\n\r\n              \r\n        // Invalidate the 'Team' tag to trigger a refetch of the team list\r\n        dispatch(teamApi.util.invalidateTags(['Team'])); \r\n       \r\n\r\n    \r\n            // Optionally, close the modal after success\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage('');\r\n            }, 2000); \r\n            \r\n            navigate('/settings');\r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n    \r\n    // Helper function to convert a file to Base64\r\n    const convertToBase64 = (file) => {\r\n        return new Promise((resolve, reject) => {\r\n            if (file instanceof File) {  \r\n                const reader = new FileReader();\r\n                reader.onloadend = () => resolve(reader.result); \r\n                reader.onerror = reject;\r\n                reader.readAsDataURL(file); \r\n            } else {\r\n                reject('The provided object is not a valid File.');\r\n            }\r\n        });\r\n    };\r\n    \r\n\r\n  const handleClose = () => {\r\n    setVisible(false);\r\n    navigate(\"/settings\");\r\n  };\r\n\r\n\r\n\r\n  \r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\">\r\n      <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\">\r\n        {/* Header - Responsive */}\r\n        <div className=\"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2 sm:space-x-3\">\r\n              <div className=\"p-1.5 sm:p-2 bg-white/20 rounded-lg\">\r\n                <Users className=\"w-4 h-4 sm:w-6 sm:h-6\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-left\">\r\n                  <h2 className=\"text-lg sm:text-xl font-semibold\">\r\n                    Edit Team\r\n                  </h2>\r\n                  <p className=\"text-white-100 text-xs sm:text-sm\">\r\n                    Update team information\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={handleClose}\r\n              className=\"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\"\r\n            >\r\n              <X className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n        {/* Form Content - Responsive */}\r\n        <div className=\"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\">\r\n          {/* Error Message - Responsive */}\r\n          {formError && (\r\n            <div className=\"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\">\r\n              <AlertCircle className=\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\" />\r\n              <div>\r\n                <p className=\"text-left font-medium text-red-800 text-sm sm:text-base\">\r\n                  Action Required\r\n                </p>\r\n                <p className=\"text-red-700 text-xs sm:text-sm\">{formError}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\r\n            {/* Responsive Grid Layout */}\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\r\n              {/* Department & Team Info Section */}\r\n              <div className=\"space-y-3 sm:space-y-4\">\r\n                <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\r\n                  <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\r\n                    <Building className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\r\n                    <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\r\n                      Organization\r\n                    </h3>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3 sm:space-y-4\">\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Department <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <Select\r\n                        options={departmentOptions}\r\n                        value={selectedDepartment}\r\n                        onChange={(option) => {\r\n                          setSelectedDepartment(option);\r\n                          setDepartmentId(option?.value || '');\r\n                        }}\r\n                        placeholder=\"Select Department\"\r\n                        className=\"w-full\"\r\n                        isSearchable\r\n                        styles={{\r\n                          control: (base, state) => ({\r\n                            ...base,\r\n                            borderRadius: '0.5rem',\r\n                            borderWidth: '2px',\r\n                            borderColor: state.isFocused ? '#3B82F6' : '#E5E7EB',\r\n                            boxShadow: state.isFocused ? '0 0 0 0 rgba(59, 130, 246, 0.1)' : 'none',\r\n                            '&:hover': {\r\n                              borderColor: '#D1D5DB'\r\n                            }\r\n                          })\r\n                        }}\r\n                      />\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\r\n                        Team Name <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={teamName}\r\n                        onChange={(e) => setTeamName(e.target.value)}\r\n                        onFocus={() => setFocusedField(\"teamName\")}\r\n                        onBlur={() => setFocusedField(null)}\r\n                        placeholder=\"Enter team name\"\r\n                        className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\r\n                          focusedField === \"teamName\"\r\n                            ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\r\n                            : \"border-gray-200 focus:border-primary hover:border-gray-300\"\r\n                        }`}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n                {/* POC */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"poc\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    POC\r\n                  </label>\r\n                  <Select\r\n                    options={pocOptions}\r\n                    value={selectedPoc}\r\n                    onChange={(option) => {\r\n                      setSelectedPoc(option);\r\n                      setPoc(option?.value || '');\r\n                    }}\r\n                    placeholder=\"Select POC\"\r\n                    className=\"w-full\"\r\n                    isSearchable\r\n                    noOptionsMessage={() => \"No options available\"}\r\n                  />\r\n                </div>\r\n\r\n                {/* Manager */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"manager\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Manager\r\n                  </label>\r\n                  <Select\r\n                    options={managerOptions}\r\n                    value={selectedManager}\r\n                    onChange={(option) => {\r\n                      setSelectedManager(option);\r\n                      setManager(option?.value || '');\r\n                    }}\r\n                    placeholder=\"Select Manager\"\r\n                    className=\"w-full\"\r\n                    isSearchable\r\n                    noOptionsMessage={() => \"No managers found\"}\r\n                  />\r\n                </div>\r\n\r\n                {/* Team Lead */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"teamLead\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Team Lead\r\n                  </label>\r\n                  {/* <select\r\n                    id=\"teamLead\"\r\n                    value={teamLead}\r\n                    onChange={(e) => setTeamLead(e.target.value)}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  >\r\n                    <option value=\"\">Select Team Lead</option>\r\n                    {users\r\n                      .filter((u) =>\r\n                        u.roles?.some((r) =>\r\n                          [\r\n                            \"team-lead\",\r\n                            \"manager\",\r\n                            \"hod\",\r\n                            \"admin\",\r\n                            \"super-admin\",\r\n                          ].includes(r)\r\n                        )\r\n                      )\r\n                      .map((u) => (\r\n                        <option key={u.id} value={u.fullName}>\r\n                          {u.fullName}\r\n                        </option>\r\n                      ))}\r\n                  </select> */}\r\n                   <Select\r\n      options={teamLeadOptions}\r\n      value={selectedTeamLead}\r\n      onChange={(option) => {\r\n        setSelectedTeamLead(option);\r\n        setTeamLead(option?.value || '');\r\n      }}\r\n      placeholder=\"Select Team Lead\"\r\n      className=\"w-full\"\r\n      isSearchable\r\n    />\r\n                </div>\r\n\r\n                {/* Workday */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                    Work Days *\r\n                  </label>\r\n                  <Select\r\n                    isMulti\r\n                    options={daysOfWeek}\r\n                    value={workday.map(day => ({ value: day, label: day }))}\r\n                    onChange={handleWorkdayChange}\r\n                    placeholder=\"Select Work Days\"\r\n                    className=\"w-full\"\r\n                    isDisabled={loading}\r\n                    noOptionsMessage={() => \"No options available\"}\r\n                  />\r\n                  {workday.length > 0 && (\r\n                    <p className=\"text-xs text-gray-500 mt-2\">\r\n                      Selected: {workday.join(\", \")}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Billable Hours */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"billableHours\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Billable Hours\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    id=\"billableHours\"\r\n                    value={billableHours}\r\n                    onChange={(e) => setBillableHours(e.target.value)}\r\n                    min=\"0\"\r\n                    placeholder=\"Enter billable hours\"\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Launch */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"launch\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Launch Date\r\n                  </label>\r\n                  <input\r\n                    type=\"date\"\r\n                    id=\"launch\"\r\n                    value={launch}\r\n                    onChange={(e) => setLaunch(e.target.value)}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                  />\r\n                </div>\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4 opacity-0\"></div>\r\n\r\n                {/* Icon Preview */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"icon\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Icon\r\n                  </label>\r\n                  {icon && icon instanceof File ? (\r\n                    <>\r\n                      <img\r\n                        src={URL.createObjectURL(icon)}\r\n                        alt=\"Icon Preview\"\r\n                        className=\"w-20 h-20 mb-2\"\r\n                      />\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"icon\"\r\n                        onChange={(e) => setIcon(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {(existingIcon || icon) && (\r\n                        <div className=\"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\">\r\n                          <img\r\n                            // Prefer stored path when string\r\n                            src={\r\n                              typeof icon === \"string\" && icon\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}`\r\n                                : existingIcon\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}`\r\n                                : \"\"\r\n                            }\r\n                            alt=\"Icon Preview\"\r\n                            className=\"w-auto h-auto object-cover\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"icon\"\r\n                        onChange={(e) => setIcon(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Logo Preview */}\r\n                <div className=\"mb-4 w-full sm:w-1/2 px-4\">\r\n                  <label\r\n                    htmlFor=\"logo\"\r\n                    className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                  >\r\n                    Logo\r\n                  </label>\r\n                  {logo && logo instanceof File ? (\r\n                    <>\r\n                      <img\r\n                        src={URL.createObjectURL(logo)}\r\n                        alt=\"Logo Preview\"\r\n                        className=\"w-20 h-20 mb-2\"\r\n                      />\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"logo\"\r\n                        onChange={(e) => setLogo(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {(existingLogo || logo) && (\r\n                        <div className=\"w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg\">\r\n                          <img\r\n                            // Prefer stored path when string\r\n                            src={\r\n                              typeof logo === \"string\" && logo\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}`\r\n                                : existingLogo\r\n                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}`\r\n                                : \"\"\r\n                            }\r\n                            alt=\"Logo Preview\"\r\n                            className=\"w-auto h-auto object-cover\"\r\n                          />\r\n                        </div>\r\n                      )}\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"logo\"\r\n                        onChange={(e) => setLogo(e.target.files[0])}\r\n                        className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n                      />\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"text-left pt-6\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                >\r\n                  <span class=\"material-symbols-rounded text-white text-xl font-regular\">\r\n                    add_circle\r\n                  </span>\r\n                  {loading ? \"Updating...\" : \"Update Team\"}\r\n                </button>\r\n              </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EditTeam;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SACEC,CAAC,EACDC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,MAAMG,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8C,IAAI,EAAEC,OAAO,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkD,GAAG,EAAEC,MAAM,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgE,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkE,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC8E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACgF,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMsF,iBAAiB,GAAGlD,WAAW,CAACmD,GAAG,CAACC,IAAI,KAAK;IACjDC,KAAK,EAAED,IAAI,CAACE,EAAE;IACdC,KAAK,EAAEH,IAAI,CAACI;EACd,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,UAAU,GAAG3D,KAAK,CACrB4D,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,oBAAA;IACd,MAAMC,sBAAsB,IAAAD,oBAAA,GAAGD,IAAI,CAACG,cAAc,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBG,IAAI,CAACC,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IACvH,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;EAEL,MAAME,cAAc,GAAGvE,KAAK,CACzB4D,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAW,qBAAA;IACd,MAAMT,sBAAsB,IAAAS,qBAAA,GAAGX,IAAI,CAACG,cAAc,cAAAQ,qBAAA,uBAAnBA,qBAAA,CAAqBP,IAAI,CAACC,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IAC1G,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;EAEL,MAAMI,eAAe,GAAGzE,KAAK,CAC1B4D,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAa,qBAAA;IACd,MAAMX,sBAAsB,IAAAW,qBAAA,GAAGb,IAAI,CAACG,cAAc,cAAAU,qBAAA,uBAAnBA,qBAAA,CAAqBT,IAAI,CAACC,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,EAAE,CAACR,IAAI,IAAIQ,EAAE,CAAC,CAAC;IACvH,MAAME,YAAY,GAAGP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IACjE,OAAOP,sBAAsB,IAAIK,YAAY;EAC/C,CAAC,CAAC,CACDf,GAAG,CAACQ,IAAI,KAAK;IACZN,KAAK,EAAEM,IAAI,CAACQ,QAAQ;IACpBZ,KAAK,EAAEI,IAAI,CAACQ;EACd,CAAC,CAAC,CAAC;;EAEL;EACAM,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE5E,KAAK,CAAC6E,MAAM,CAAC;EACpDF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEL,cAAc,CAACM,MAAM,CAAC;EACjEF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEjB,UAAU,CAACkB,MAAM,CAAC;EACzDF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,eAAe,CAACI,MAAM,CAAC;;EAGpE;EACA,MAAMC,UAAU,GAAG,CACjB;IAAEvB,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEF,KAAK,EAAE,SAAS;IAAEE,KAAK,EAAE;EAAU,CAAC,EACtC;IAAEF,KAAK,EAAE,WAAW;IAAEE,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,CACrC;;EAED;EACA,MAAMsB,mBAAmB,GAAIC,eAAe,IAAK;IAC/C,MAAMC,cAAc,GAAGD,eAAe,GAAGA,eAAe,CAAC3B,GAAG,CAAC6B,MAAM,IAAIA,MAAM,CAAC3B,KAAK,CAAC,GAAG,EAAE;IACzFhC,UAAU,CAAC0D,cAAc,CAAC;EAC5B,CAAC;EAEDpH,SAAS,CAAC,MAAM;IACd,MAAMsH,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAAC9F,YAAY,CAAC,CAAC,EAAE;QACnB0C,QAAQ,CAAC,gCAAgC,CAAC;QAC1CY,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMrD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAErC,IAAI;QACA;QACA,MAAM4F,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAGpG,OAAO,QAAQ,EAAE;UAClDqG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUjG,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEV,IAAI,CAAC8F,aAAa,CAACI,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEA,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5C;QACA1F,QAAQ,CACNyF,SAAS,CAACrC,GAAG,CAAEQ,IAAI,KAAM;UACvBL,EAAE,EAAEK,IAAI,CAACL,EAAE;UACXa,QAAQ,EAAE,GAAG,CAACR,IAAI,CAAC+B,KAAK,IAAI,EAAE,EAAEtB,IAAI,CAAC,CAAC,IAAI,CACxCT,IAAI,CAACgC,KAAK,IAAI,EAAE,EAChBvB,IAAI,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;UACjBsB,KAAK,EAAE/B,IAAI,CAAC+B,KAAK;UACjBC,KAAK,EAAEhC,IAAI,CAACgC,KAAK;UACjBC,KAAK,EAAEC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAACiC,KAAK,CAAC,GAC5BjC,IAAI,CAACiC,KAAK,CAACzC,GAAG,CAAE4C,CAAC,IAAK,CAACA,CAAC,CAACvC,IAAI,IAAI,EAAE,EAAEY,IAAI,CAAC,CAAC,CAAC,GAC5C,EAAE;UACNN,cAAc,EAAE+B,KAAK,CAACC,OAAO,CAACnC,IAAI,CAACG,cAAc,CAAC,GAC9CH,IAAI,CAACG,cAAc,CAACX,GAAG,CAAEa,EAAE,IAAK,CAACA,EAAE,CAACR,IAAI,IAAI,EAAE,EAAEY,IAAI,CAAC,CAAC,CAAC,GACvD;QACN,CAAC,CAAC,CACJ,CAAC;;QAEO;QACA,MAAM4B,mBAAmB,GAAG,MAAMb,KAAK,CAAC,GAAGpG,OAAO,cAAc,EAAE;UAC9DqG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUjG,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEV,IAAI,CAAC4G,mBAAmB,CAACV,EAAE,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,MAAMU,eAAe,GAAG,MAAMD,mBAAmB,CAACP,IAAI,CAAC,CAAC;QACxDxF,cAAc,CAACgG,eAAe,CAACjG,WAAW,CAAC;;QAEnC;QACA,IAAIR,WAAW,EAAE;UACb,MAAM0G,YAAY,GAAG,MAAMf,KAAK,CAAC,GAAGpG,OAAO,UAAUS,WAAW,EAAE,EAAE;YAChE4F,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACL,eAAe,EAAE,UAAUjG,KAAK,EAAE;cAClC,cAAc,EAAE;YACpB;UACJ,CAAC,CAAC;UAEZ,IAAI,CAAC8G,YAAY,CAACZ,EAAE,EAAE;YACpB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;UACjD;UAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAACT,IAAI,CAAC,CAAC;;UAE1C;UACAtF,OAAO,CAACgG,QAAQ,CAACjG,IAAI,CAAC;;UAEtB;UACAG,WAAW,CAAC8F,QAAQ,CAACjG,IAAI,CAACsD,IAAI,IAAI,EAAE,CAAC;UACrCjD,OAAO,CAAC4F,QAAQ,CAACjG,IAAI,CAACI,IAAI,IAAI,IAAI,CAAC;UACnCK,OAAO,CAACwF,QAAQ,CAACjG,IAAI,CAACQ,IAAI,IAAI,IAAI,CAAC;UACnCK,MAAM,CAACoF,QAAQ,CAACjG,IAAI,CAACY,GAAG,IAAI,EAAE,CAAC;UAC/BG,UAAU,CAACkF,QAAQ,CAACjG,IAAI,CAACc,OAAO,IAAI,EAAE,CAAC;UACvCG,WAAW,CAACgF,QAAQ,CAACjG,IAAI,CAACkG,SAAS,IAAI,EAAE,CAAC;UAC1C;UACA,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAIF,QAAQ,CAACjG,IAAI,CAACkB,OAAO,EAAE;YACzB,IAAI;cACFiF,YAAY,GAAG,OAAOF,QAAQ,CAACjG,IAAI,CAACkB,OAAO,KAAK,QAAQ,GACpDkF,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAACjG,IAAI,CAACkB,OAAO,CAAC,GACjC+E,QAAQ,CAACjG,IAAI,CAACkB,OAAO;YAC3B,CAAC,CAAC,OAAOoF,CAAC,EAAE;cACV/B,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAE4E,CAAC,CAAC;cAC1CH,YAAY,GAAG,EAAE;YACnB;UACF;UACAhF,UAAU,CAACwE,KAAK,CAACC,OAAO,CAACO,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;UAC3D9E,gBAAgB,CAAC4E,QAAQ,CAACjG,IAAI,CAACuG,cAAc,IAAI,EAAE,CAAC;UACpDhF,SAAS,CAAC0E,QAAQ,CAACjG,IAAI,CAACsB,MAAM,IAAI,EAAE,CAAC;UACrCf,eAAe,CAAC0F,QAAQ,CAACjG,IAAI,CAACI,IAAI,IAAI,EAAE,CAAC;UACzCO,eAAe,CAACsF,QAAQ,CAACjG,IAAI,CAACQ,IAAI,IAAI,EAAE,CAAC;;UAEzC;UACA,MAAMgB,YAAY,GAChByE,QAAQ,CAACjG,IAAI,CAACF,WAAW,IAAImG,QAAQ,CAACjG,IAAI,CAACF,WAAW,CAAC2E,MAAM,GAAG,CAAC,GAC7DwB,QAAQ,CAACjG,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC,CAACsD,EAAE,GAC/B,EAAE;UACR3B,eAAe,CAACD,YAAY,CAAC;;UAE7B;UACAgF,UAAU,CAAC,MAAM;YACf,IAAIhF,YAAY,EAAE;cAChB,MAAMiF,UAAU,GAAGV,eAAe,CAACjG,WAAW,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAK5B,YAAY,CAAC;cAC/E,IAAIiF,UAAU,EAAE;gBACdhE,qBAAqB,CAAC;kBAAEU,KAAK,EAAEsD,UAAU,CAACrD,EAAE;kBAAEC,KAAK,EAAEoD,UAAU,CAACnD;gBAAK,CAAC,CAAC;cACzE;YACF;YAEA,IAAI2C,QAAQ,CAACjG,IAAI,CAACY,GAAG,EAAE;cACrB+B,cAAc,CAAC;gBAAEQ,KAAK,EAAE8C,QAAQ,CAACjG,IAAI,CAACY,GAAG;gBAAEyC,KAAK,EAAE4C,QAAQ,CAACjG,IAAI,CAACY;cAAI,CAAC,CAAC;YACxE;YAEA,IAAIqF,QAAQ,CAACjG,IAAI,CAACc,OAAO,EAAE;cACzB+B,kBAAkB,CAAC;gBAAEM,KAAK,EAAE8C,QAAQ,CAACjG,IAAI,CAACc,OAAO;gBAAEuC,KAAK,EAAE4C,QAAQ,CAACjG,IAAI,CAACc;cAAQ,CAAC,CAAC;YACpF;YAEA,IAAImF,QAAQ,CAACjG,IAAI,CAACkG,SAAS,EAAE;cAC3BnD,mBAAmB,CAAC;gBAAEI,KAAK,EAAE8C,QAAQ,CAACjG,IAAI,CAACkG,SAAS;gBAAE7C,KAAK,EAAE4C,QAAQ,CAACjG,IAAI,CAACkG;cAAU,CAAC,CAAC;YACzF;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,CAAC,OAAOxE,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACkF,OAAO,CAAC;MACzB,CAAC,SAAS;QACRrE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACzF,WAAW,CAAC,CAAC;;EAEjB;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMoJ,MAAM,GAAG1H,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAIyH,MAAM,EAAE;MACVxE,eAAe,CAACwE,MAAM,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAOC,KAAK,IAAK;IACpCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBrF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd;IACA,MAAMsF,SAAS,GAAG7E,YAAY;IAE1B,IAAI,CAAC6E,SAAS,EAAE;MACZtF,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACJ;IAEA,IAAI;MACA,MAAMzC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACRyC,QAAQ,CAAC,kCAAkC,CAAC;QAC5C;MACJ;;MAEA;MACA,MAAMuF,kBAAkB,GAAG,MAAMjC,KAAK,CAAC,GAAGpG,OAAO,eAAe,EAAE;QAC9DqG,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUjG,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgI,kBAAkB,CAAC9B,EAAE,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MAC1D;MAEA,MAAM8B,cAAc,GAAG,MAAMD,kBAAkB,CAAC3B,IAAI,CAAC,CAAC;MACtD,MAAMtB,QAAQ,GAAG,GAAGkD,cAAc,CAAC3B,KAAK,IAAI2B,cAAc,CAAC1B,KAAK,EAAE;MAClE;;MAEA;MACA,MAAM2B,WAAW,GAAG;QAChB9D,IAAI,EAAEpD,QAAQ,CAACgE,IAAI,CAAC,CAAC;QACrBmD,aAAa,EAAEC,QAAQ,CAAC9F,YAAY,CAAC;QACrCF,MAAM,EAAEA,MAAM;QACdJ,OAAO,EAAEA,OAAO;QAChBqF,cAAc,EAAEnF,aAAa,GAAGkG,QAAQ,CAAClG,aAAa,CAAC,GAAG,IAAI;QAC9DR,GAAG,EAAEA,GAAG;QACRE,OAAO,EAAEA,OAAO;QAChBoF,SAAS,EAAElF,QAAQ;QACnBuG,UAAU,EAAEN;MAChB,CAAC;;MAED;MACA,IAAI7G,IAAI,IAAIA,IAAI,YAAYoH,IAAI,EAAE;QAAE;QAChC,MAAMC,UAAU,GAAG,MAAMC,eAAe,CAACtH,IAAI,CAAC;QAC9CgH,WAAW,CAAChH,IAAI,GAAGqH,UAAU;MACjC,CAAC,MAAM;QACHlD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC/C;MAEA,IAAIhE,IAAI,IAAIA,IAAI,YAAYgH,IAAI,EAAE;QAAE;QAChC,MAAMG,UAAU,GAAG,MAAMD,eAAe,CAAClH,IAAI,CAAC;QAC9C4G,WAAW,CAAC5G,IAAI,GAAGmH,UAAU;MACjC,CAAC,MAAM;QACHpD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC/C;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE4C,WAAW,CAAC;;MAE3D;MACA,MAAMQ,QAAQ,GAAG,MAAM3C,KAAK,CAAC,GAAGpG,OAAO,UAAUS,WAAW,EAAE,EAAE;QAC5D4F,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUjG,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB,CAAC;QACD2I,IAAI,EAAEzB,IAAI,CAAC0B,SAAS,CAACV,WAAW,CAAC,CAAE;MACvC,CAAC,CAAC;MAEF,IAAI,CAACQ,QAAQ,CAACxC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAGuC,QAAQ,CAACG,UAAU,CAAC;MACpE;MAEA,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACrC,IAAI,CAAC,CAAC;MACpC;MACA3H,YAAY,CAAC,SAAS,CAAC;;MAG3B;MACA8B,QAAQ,CAAC3B,OAAO,CAACkK,IAAI,CAACC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;;MAI3C;MACA1B,UAAU,CAAC,MAAM;QACbhH,UAAU,CAAC,KAAK,CAAC;QACjB2C,iBAAiB,CAAC,EAAE,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;MAERxC,QAAQ,CAAC,WAAW,CAAC;IACzB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACZ9D,YAAY,CAAC,OAAO,CAAC;IACzB;EACJ,CAAC;;EAED;EACA,MAAM8J,eAAe,GAAIS,IAAI,IAAK;IAC9B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAIH,IAAI,YAAYX,IAAI,EAAE;QACtB,MAAMe,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAACP,MAAM,CAAC;QAC/CO,MAAM,CAACG,OAAO,GAAGJ,MAAM;QACvBC,MAAM,CAACI,aAAa,CAACR,IAAI,CAAC;MAC9B,CAAC,MAAM;QACHG,MAAM,CAAC,0CAA0C,CAAC;MACtD;IACJ,CAAC,CAAC;EACN,CAAC;EAGH,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBpJ,UAAU,CAAC,KAAK,CAAC;IACjBG,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAKD,IAAI,CAACJ,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEb,OAAA;IAAKmK,SAAS,EAAC,6HAA6H;IAAAC,QAAA,eAC1IpK,OAAA;MAAKmK,SAAS,EAAC,iMAAiM;MAAAC,QAAA,gBAE9MpK,OAAA;QAAKmK,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DpK,OAAA;UAAKmK,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpK,OAAA;YAAKmK,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDpK,OAAA;cAAKmK,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClDpK,OAAA,CAACT,KAAK;gBAAC4K,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNxK,OAAA;cAAAoK,QAAA,eACEpK,OAAA;gBAAKmK,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpK,OAAA;kBAAImK,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxK,OAAA;kBAAGmK,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxK,OAAA;YACEyK,OAAO,EAAEP,WAAY;YACrBC,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpFpK,OAAA,CAACV,CAAC;cAAC6K,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxK,OAAA;QAAKmK,SAAS,EAAC,iFAAiF;QAAAC,QAAA,GAE7FhH,SAAS,iBACRpD,OAAA;UAAKmK,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,gBACtKpK,OAAA,CAACH,WAAW;YAACsK,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFxK,OAAA;YAAAoK,QAAA,gBACEpK,OAAA;cAAGmK,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxK,OAAA;cAAGmK,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEhH;YAAS;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDxK,OAAA;UAAM0K,QAAQ,EAAEtC,YAAa;UAAC+B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAE9DpK,OAAA;YAAKmK,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAE7DpK,OAAA;cAAKmK,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCpK,OAAA;gBAAKmK,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACpFpK,OAAA;kBAAKmK,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,gBACvDpK,OAAA,CAACR,QAAQ;oBAAC2K,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5DxK,OAAA;oBAAImK,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAENxK,OAAA;kBAAKmK,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCpK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBAAOmK,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,aAClF,eAAApK,OAAA;wBAAMmK,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACRxK,OAAA,CAACb,MAAM;sBACLwL,OAAO,EAAErG,iBAAkB;sBAC3BG,KAAK,EAAEX,kBAAmB;sBAC1B8G,QAAQ,EAAGxE,MAAM,IAAK;wBACpBrC,qBAAqB,CAACqC,MAAM,CAAC;wBAC7BrD,eAAe,CAAC,CAAAqD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;sBACtC,CAAE;sBACFoG,WAAW,EAAC,mBAAmB;sBAC/BV,SAAS,EAAC,QAAQ;sBAClBW,YAAY;sBACZC,MAAM,EAAE;wBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,MAAM;0BACzB,GAAGD,IAAI;0BACPE,YAAY,EAAE,QAAQ;0BACtBC,WAAW,EAAE,KAAK;0BAClBC,WAAW,EAAEH,KAAK,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;0BACpDC,SAAS,EAAEL,KAAK,CAACI,SAAS,GAAG,iCAAiC,GAAG,MAAM;0BACvE,SAAS,EAAE;4BACTD,WAAW,EAAE;0BACf;wBACF,CAAC;sBACH;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBAAOmK,SAAS,EAAC,6EAA6E;sBAAAC,QAAA,GAAC,YACnF,eAAApK,OAAA;wBAAMmK,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACRxK,OAAA;sBACEwL,IAAI,EAAC,MAAM;sBACX/G,KAAK,EAAEjD,QAAS;sBAChBoJ,QAAQ,EAAGhD,CAAC,IAAKnG,WAAW,CAACmG,CAAC,CAAC6D,MAAM,CAAChH,KAAK,CAAE;sBAC7CiH,OAAO,EAAEA,CAAA,KAAMnI,eAAe,CAAC,UAAU,CAAE;sBAC3CoI,MAAM,EAAEA,CAAA,KAAMpI,eAAe,CAAC,IAAI,CAAE;sBACpCsH,WAAW,EAAC,iBAAiB;sBAC7BV,SAAS,EAAE,8HACT7G,YAAY,KAAK,UAAU,GACvB,oDAAoD,GACpD,4DAA4D;oBAC/D;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGJxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,KAAK;gBACbzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxK,OAAA,CAACb,MAAM;gBACLwL,OAAO,EAAE9F,UAAW;gBACpBJ,KAAK,EAAET,WAAY;gBACnB4G,QAAQ,EAAGxE,MAAM,IAAK;kBACpBnC,cAAc,CAACmC,MAAM,CAAC;kBACtBjE,MAAM,CAAC,CAAAiE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBAC7B,CAAE;gBACFoG,WAAW,EAAC,YAAY;gBACxBV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;gBACZe,gBAAgB,EAAEA,CAAA,KAAM;cAAuB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,SAAS;gBACjBzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxK,OAAA,CAACb,MAAM;gBACLwL,OAAO,EAAElF,cAAe;gBACxBhB,KAAK,EAAEP,eAAgB;gBACvB0G,QAAQ,EAAGxE,MAAM,IAAK;kBACpBjC,kBAAkB,CAACiC,MAAM,CAAC;kBAC1B/D,UAAU,CAAC,CAAA+D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBACjC,CAAE;gBACFoG,WAAW,EAAC,gBAAgB;gBAC5BV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;gBACZe,gBAAgB,EAAEA,CAAA,KAAM;cAAoB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,UAAU;gBAClBzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eA0BPxK,OAAA,CAACb,MAAM;gBACpBwL,OAAO,EAAEhF,eAAgB;gBACzBlB,KAAK,EAAEL,gBAAiB;gBACxBwG,QAAQ,EAAGxE,MAAM,IAAK;kBACpB/B,mBAAmB,CAAC+B,MAAM,CAAC;kBAC3B7D,WAAW,CAAC,CAAA6D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3B,KAAK,KAAI,EAAE,CAAC;gBAClC,CAAE;gBACFoG,WAAW,EAAC,kBAAkB;gBAC9BV,SAAS,EAAC,QAAQ;gBAClBW,YAAY;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBAAOmK,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxK,OAAA,CAACb,MAAM;gBACL2M,OAAO;gBACPnB,OAAO,EAAE3E,UAAW;gBACpBvB,KAAK,EAAEjC,OAAO,CAAC+B,GAAG,CAACwH,GAAG,KAAK;kBAAEtH,KAAK,EAAEsH,GAAG;kBAAEpH,KAAK,EAAEoH;gBAAI,CAAC,CAAC,CAAE;gBACxDnB,QAAQ,EAAE3E,mBAAoB;gBAC9B4E,WAAW,EAAC,kBAAkB;gBAC9BV,SAAS,EAAC,QAAQ;gBAClB6B,UAAU,EAAEpI,OAAQ;gBACpBiI,gBAAgB,EAAEA,CAAA,KAAM;cAAuB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDhI,OAAO,CAACuD,MAAM,GAAG,CAAC,iBACjB/F,OAAA;gBAAGmK,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,YAC9B,EAAC5H,OAAO,CAACyJ,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,eAAe;gBACvBzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxK,OAAA;gBACEwL,IAAI,EAAC,QAAQ;gBACb9G,EAAE,EAAC,eAAe;gBAClBD,KAAK,EAAE/B,aAAc;gBACrBkI,QAAQ,EAAGhD,CAAC,IAAKjF,gBAAgB,CAACiF,CAAC,CAAC6D,MAAM,CAAChH,KAAK,CAAE;gBAClDyH,GAAG,EAAC,GAAG;gBACPrB,WAAW,EAAC,sBAAsB;gBAClCV,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,QAAQ;gBAChBzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxK,OAAA;gBACEwL,IAAI,EAAC,MAAM;gBACX9G,EAAE,EAAC,QAAQ;gBACXD,KAAK,EAAE7B,MAAO;gBACdgI,QAAQ,EAAGhD,CAAC,IAAK/E,SAAS,CAAC+E,CAAC,CAAC6D,MAAM,CAAChH,KAAK,CAAE;gBAC3C0F,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxK,OAAA;cAAKmK,SAAS,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG3DxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,MAAM;gBACdzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACP9I,IAAI,IAAIA,IAAI,YAAYoH,IAAI,gBAC3B9I,OAAA,CAAAE,SAAA;gBAAAkK,QAAA,gBACEpK,OAAA;kBACEmM,GAAG,EAAEC,GAAG,CAACC,eAAe,CAAC3K,IAAI,CAAE;kBAC/B4K,GAAG,EAAC,cAAc;kBAClBnC,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACFxK,OAAA;kBACEwL,IAAI,EAAC,MAAM;kBACX9G,EAAE,EAAC,MAAM;kBACTkG,QAAQ,EAAGhD,CAAC,IAAKjG,OAAO,CAACiG,CAAC,CAAC6D,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CpC,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CAAC,gBAEHxK,OAAA,CAAAE,SAAA;gBAAAkK,QAAA,GACG,CAACxI,YAAY,IAAIF,IAAI,kBACpB1B,OAAA;kBAAKmK,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACnEpK,OAAA;oBACE;oBACAmM,GAAG,EACD,OAAOzK,IAAI,KAAK,QAAQ,IAAIA,IAAI,GAC5B,GAAGtB,OAAO,CAACC,GAAG,CAACmM,0BAA0B,IAAI9K,IAAI,EAAE,GACnDE,YAAY,GACZ,GAAGxB,OAAO,CAACC,GAAG,CAACmM,0BAA0B,IAAI5K,YAAY,EAAE,GAC3D,EACL;oBACD0K,GAAG,EAAC,cAAc;oBAClBnC,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eACDxK,OAAA;kBACEwL,IAAI,EAAC,MAAM;kBACX9G,EAAE,EAAC,MAAM;kBACTkG,QAAQ,EAAGhD,CAAC,IAAKjG,OAAO,CAACiG,CAAC,CAAC6D,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CpC,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNxK,OAAA;cAAKmK,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpK,OAAA;gBACE4L,OAAO,EAAC,MAAM;gBACdzB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACP1I,IAAI,IAAIA,IAAI,YAAYgH,IAAI,gBAC3B9I,OAAA,CAAAE,SAAA;gBAAAkK,QAAA,gBACEpK,OAAA;kBACEmM,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACvK,IAAI,CAAE;kBAC/BwK,GAAG,EAAC,cAAc;kBAClBnC,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACFxK,OAAA;kBACEwL,IAAI,EAAC,MAAM;kBACX9G,EAAE,EAAC,MAAM;kBACTkG,QAAQ,EAAGhD,CAAC,IAAK7F,OAAO,CAAC6F,CAAC,CAAC6D,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CpC,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CAAC,gBAEHxK,OAAA,CAAAE,SAAA;gBAAAkK,QAAA,GACG,CAACpI,YAAY,IAAIF,IAAI,kBACpB9B,OAAA;kBAAKmK,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACnEpK,OAAA;oBACE;oBACAmM,GAAG,EACD,OAAOrK,IAAI,KAAK,QAAQ,IAAIA,IAAI,GAC5B,GAAG1B,OAAO,CAACC,GAAG,CAACmM,0BAA0B,IAAI1K,IAAI,EAAE,GACnDE,YAAY,GACZ,GAAG5B,OAAO,CAACC,GAAG,CAACmM,0BAA0B,IAAIxK,YAAY,EAAE,GAC3D,EACL;oBACDsK,GAAG,EAAC,cAAc;oBAClBnC,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eACDxK,OAAA;kBACEwL,IAAI,EAAC,MAAM;kBACX9G,EAAE,EAAC,MAAM;kBACTkG,QAAQ,EAAGhD,CAAC,IAAK7F,OAAO,CAAC6F,CAAC,CAAC6D,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE;kBAC5CpC,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxK,OAAA;YAAKmK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BpK,OAAA;cACEwL,IAAI,EAAC,QAAQ;cACbrB,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvIpK,OAAA;gBAAMyM,KAAK,EAAC,0DAA0D;gBAAArC,QAAA,EAAC;cAEvE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACN5G,OAAO,GAAG,aAAa,GAAG,aAAa;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzJ,EAAA,CA5uBIJ,QAAQ;EAAA,QACKvB,WAAW,EACXH,WAAW;AAAA;AAAAyN,EAAA,GAFxB/L,QAAQ;AA8uBd,eAAeA,QAAQ;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}